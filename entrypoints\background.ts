import { defineBackground } from 'wxt/background';
import { GoogleGenerativeAI, GenerativeModel, Part } from '@google/generative-ai';

// API Keys - In a real production app, these would be securely stored
const GEMINI_API_KEY = import.meta.env.GEMINI_API_KEY || 'AIzaSyDRkj87uewFTRlC4bpg_vXUSNe4I_INMPM';
const GROK_API_KEY = import.meta.env.GROK_API_KEY || '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';
const DEEPSEEK_API_KEY = import.meta.env.DEEPSEEK_API_KEY || '***********************************';

// Initialize Gemini API
const genAI = new GoogleGenerativeAI(GEMINI_API_KEY);

interface LlmRequestPrompt {
  type?: 'text' | 'image' | 'file';
  text?: string;
  image?: string; // Data URL for image
  file?: string;  // Text content for file
  url?: string;   // Current page URL
  selectedText?: string; // User-selected text
}

export default defineBackground(() => {
  console.log('Moko Browser AI Assistant initialized');

  // Handle messages from the sidepanel
  browser.runtime.onMessage.addListener(async (message, sender, sendResponse) => {
    // Handle navigation commands
    if (message.command) {
      const { command } = message;

      if (command.startsWith("navigate to ")) {
        const url = command.substring(12);
        browser.tabs.update({ url });
      } else if (command.startsWith("search for ")) {
        const query = command.substring(11);
        browser.tabs.create({ url: `https://www.google.com/search?q=${query}` });
      }
      return;
    }

    // Handle LLM requests
    if (message.type === 'llmRequest') {
      const { modelName, prompt } = message;

      try {
        let responseText = '';

        // Process based on the selected model
        switch (modelName) {
          case 'gemini-pro':
            const model = genAI.getGenerativeModel({ model: 'gemini-pro' });
            const result = await model.generateContent(typeof prompt === 'string' ? prompt : prompt.text || '');
            const response = await result.response;
            responseText = response.text();
            break;

          case 'grok':
            // Simulate Grok API call (replace with actual API in production)
            responseText = `[Grok API] I understand you're asking: "${typeof prompt === 'string' ? prompt : prompt.text || ''}". This is a simulated response. In production, this would connect to the actual Grok API.`;
            break;

          case 'deepseek':
            // Simulate DeepSeek API call (replace with actual API in production)
            responseText = `[DeepSeek API] Processing your query: "${typeof prompt === 'string' ? prompt : prompt.text || ''}". This is a simulated response. In production, this would connect to the actual DeepSeek API.`;
            break;

          default:
            throw new Error(`Unknown model: ${modelName}`);
        }

        sendResponse({ success: true, response: responseText });
      } catch (error) {
        console.error('LLM request error:', error);
        sendResponse({
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }

      return true; // Indicates that sendResponse will be called asynchronously
    }
  });
});