import { defineBackground } from 'wxt/background';

export default defineBackground(() => {
  browser.runtime.onMessage.addListener((message: { command: string }, sender, sendResponse) => {
    const { command } = message;

    if (command.startsWith("navigate to ")) {
      const url = command.substring(12);
      browser.tabs.update({ url });
    } else if (command.startsWith("search for ")) {
      const query = command.substring(11);
      browser.tabs.create({ url: `https://www.google.com/search?q=${query}` });
    }
  });
});