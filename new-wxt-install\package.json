{"name": "new-wxt-install", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "wxt build", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.7.0", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.8.3", "wxt": "^0.20.7"}}