/// <reference types="chrome" />
import { GoogleGenerativeAI, GenerativeModel, Part } from '@google/generative-ai';

// IMPORTANT: In a real application, store API keys securely (e.g., environment variables, backend service).
// For this demonstration, they are hardcoded for simplicity.
const GEMINI_API_KEY: string = (process.env.GEMINI_API_KEY as string) || '';
const GROK_API_KEY: string = (process.env.GROK_API_KEY as string) || '';
const DEEPSEEK_API_KEY: string = (process.env.DEEPSEEK_API_KEY as string) || '';

const genAI = new GoogleGenerativeAI(GEMINI_API_KEY);

chrome.runtime.onInstalled.addListener(() => {
  console.log('Moko Browser Robust extension installed.');
});

interface LlmRequestPrompt {
  type: 'text' | 'image' | 'file';
  text?: string;
  image?: string; // Data URL for image
  file?: string;  // Text content for file
  url?: string;   // New: Current page URL
  selectedText?: string; // New: User-selected text
}

// New: Function to process commands
async function processCommand(promptText: string, senderTabId: number | undefined, modelName: string): Promise<string | null> {
  const lowerCasePrompt = promptText.toLowerCase();

  if (lowerCasePrompt.includes('summarize') && lowerCasePrompt.includes('page')) {
    if (senderTabId) {
      try {
        const response = await chrome.tabs.sendMessage(senderTabId, { type: 'getPageContent' });
        if (response && response.content) {
          const pageContent = response.content;
          // Summarize using LLM
          const model = genAI.getGenerativeModel({ model: 'gemini-pro' }); // Always use gemini-pro for summarization for now
          const summarizationPrompt = `Please summarize the following text from a webpage:

${pageContent.substring(0, 10000)}... (truncated if very long)

Summary:`; // Truncate to avoid exceeding token limits

          const result = await model.generateContent(summarizationPrompt);
          const summaryResponse = await result.response;
          return summaryResponse.text();
        } else {
          return 'Could not get page content for summarization.';
        }
      } catch (error: unknown) {
        console.error('Error sending message to content script or summarizing:', error);
        return 'Error communicating with page or summarizing content.';
      }
    } else {
      return 'Cannot summarize page without an active tab context.';
    }
  }
  return null; // No command found
}

chrome.commands.onCommand.addListener(async (command: string) => {
  if (command === '_execute_side_panel_action') {
    const [tab] = await chrome.tabs.query({
      active: true,
      lastFocusedWindow: true
    });
    if (tab && tab.id) {
      // This toggles the side panel. If it's open, it closes it, and vice-versa.
      // Note: As of Manifest V3, there isn't a direct `toggle` method.
      // We open it, and if it's already open, it stays open.
      // A more sophisticated toggle would require tracking its state.
      await chrome.sidePanel.open({
        tabId: tab.id
      });
    }
  }
});

chrome.runtime.onMessage.addListener((message: any, sender: chrome.runtime.MessageSender, sendResponse: (response?: any) => void) => {
  if (message.type === 'llmRequest') {
    const { modelName, prompt } = message as { modelName: string, prompt: LlmRequestPrompt };

    async function handleLlmRequest() {
      try {
        let responseText = '';
        let contentParts: (string | Part)[] = [];

        // Check for commands first
        if (prompt.text) {
          const commandResponse = await processCommand(prompt.text, sender.tab?.id, modelName);
          if (commandResponse) {
            sendResponse({ success: true, response: commandResponse });
            return; // Command handled, no need to call LLM
          }
        }

        // ... existing LLM logic for text, image, file ...
        if (prompt.text) {
          let fullTextPrompt = prompt.text;
          if (prompt.url) {
            fullTextPrompt += `\n\n(Context URL: ${prompt.url})`;
          }
          if (prompt.selectedText) {
            fullTextPrompt += `\n\n(Selected Text: ${prompt.selectedText})`;
          }
          contentParts.push({ text: fullTextPrompt });
        }
        if (prompt.image) {
          contentParts.push({
            inlineData: {
              mimeType: prompt.image.split(';')[0].split(':')[1],
              data: prompt.image.split(',')[1],
            },
          });
        }
        if (prompt.file) {
          contentParts.push({ text: `File content: ${prompt.file}` });
        }

        // Determine which Gemini model to use based on content type
        let model: GenerativeModel;
        if (modelName === 'gemini-pro' && prompt.image) {
          model = genAI.getGenerativeModel({ model: 'gemini-pro-vision' });
        } else if (modelName === 'gemini-pro') {
          model = genAI.getGenerativeModel({ model: 'gemini-pro' });
        } else {
          // Fallback for other models or if gemini-pro-vision is not needed
          model = genAI.getGenerativeModel({ model: 'gemini-pro' }); // Default to gemini-pro
        }

        switch (modelName) {
          case 'gemini-pro':
            const result = await model.generateContent(contentParts);
            const response = await result.response;
            responseText = response.text();
            break;
          case 'grok':
            // Placeholder for Grok API integration
            responseText = `Grok integration is not yet implemented. Your prompt: ${prompt.text || 'No text provided'}`;
            break;
          case 'deepseek':
            // Placeholder for DeepSeek API integration
            responseText = `DeepSeek integration is not yet implemented. Your prompt: ${prompt.text || 'No text provided'}`;
            break;
          default:
            responseText = `Unknown model: ${modelName}`;
        }
        sendResponse({ success: true, response: responseText });
      } catch (error: unknown) {
        console.error('Error handling LLM request:', error);
        if (error instanceof Error) {
          sendResponse({ success: false, error: error.message });
        } else {
          sendResponse({ success: false, error: 'An unknown error occurred.' });
        }
      }
    }

    handleLlmRequest();
    return true; // Indicates that sendResponse will be called asynchronously
  }
});