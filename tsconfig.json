{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "module": "ESNext", "moduleResolution": "Node", "strict": true, "jsx": "react-jsx", "sourceMap": true, "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "lib": ["ESNext", "DOM"], "skipLibCheck": true, "types": ["wxt/client", "vite/client"]}, "include": ["./entrypoints/**/*.ts", "./entrypoints/**/*.tsx", "./wxt.config.ts"]}