📂 {rootDir}/
   📁 .output/
   📁 .wxt/
   📁 assets/
   📁 components/
   📁 composables/
   📁 entrypoints/
   📁 hooks/
   📁 modules/
   📁 public/
   📁 utils/
   📄 .env
   📄 .env.publish
   📄 app.config.ts
   📄 package.json
   📄 tsconfig.json
   📄 web-ext.config.ts
   📄 wxt.config.ts

  Here's a brief summary of each of these files and directories:

.output/: All build artifacts will go here
.wxt/: Generated by WXT, it contains TS config
assets/: Contains all CSS, images, and other assets that should be processed by WXT
components/: Auto-imported by default, contains UI components
composables/: Auto-imported by default, contains source code for your project's composable functions for Vue
entrypoints/: Contains all the entrypoints that get bundled into your extension
hooks/: Auto-imported by default, contains source code for your project's hooks for React and Solid
modules/: Contains local WXT Modules for your project
public/: Contains any files you want to copy into the output folder as-is, without being processed by WXT
utils/: Auto-imported by default, contains generic utilities used throughout your project
.env: Contains Environment Variables
.env.publish: Contains Environment Variables for publishing
app.config.ts: Contains Runtime Config
package.json: The standard file used by your package manager
tsconfig.json: Config telling TypeScript how to behave
web-ext.config.ts: Configure Browser Startup
wxt.config.ts: The main config file for WXT projects
Adding a src/ Directory
Many developers like having a src/ directory to separate source code from configuration files. You can enable it inside the wxt.config.ts file:

wxt.config.ts

export default defineConfig({
  srcDir: 'src',
});
After enabling it, your project structure should look like this:


📂 {rootDir}/
   📁 .output/
   📁 .wxt/
   📁 modules/
   📁 public/
   📂 src/
      📁 assets/
      📁 components/
      📁 composables/
      📁 entrypoints/
      📁 hooks/
      📁 utils/
      📄 app.config.ts
   📄 .env
   📄 .env.publish
   📄 package.json
   📄 tsconfig.json
   📄 web-ext.config.ts
   📄 wxt.config.ts
Customizing Other Directories
You can configure the following directories:

wxt.config.ts

export default defineConfig({
  // Relative to project root
  srcDir: "src",             // default: "."
  modulesDir: "wxt-modules", // default: "modules"
  outDir: "dist",            // default: ".output"
  publicDir: "static",       // default: "public"

  // Relative to srcDir
  entrypointsDir: "entries", // default: "entrypoints"
})
You can use absolute or relative paths.

Edit this page
Last updated: 31/03/2025, 12:24

Pager
Previous page
Installation 

Entrypoints
WXT uses the files inside the entrypoints/ directory as inputs when bundling your extension. They can be HTML, JS, CSS, or any variant of those file types supported by Vite (TS, JSX, SCSS, etc).

Folder Structure
Inside the entrypoints/ directory, an entrypoint is defined as a single file or directory (with an index file) inside it.


Single File

Directory

📂 entrypoints/
   📄 {name}.{ext}
The entrypoint's name dictates the type of entrypoint. For example, to add a "Background" entrypoint, either of these files would work:


Single File

Directory

📂 entrypoints/
   📄 background.ts
Refer to the Entrypoint Types section for the full list of listed entrypoints and their filename patterns.

Including Other Files
When using an entrypoint directory, entrypoints/{name}/index.{ext}, you can add related files next to the index file.


📂 entrypoints/
   📂 popup/
      📄 index.html     ← This file is the entrypoint
      📄 main.ts
      📄 style.css
   📂 background/
      📄 index.ts       ← This file is the entrypoint
      📄 alarms.ts
      📄 messaging.ts
   📂 youtube.content/
      📄 index.ts       ← This file is the entrypoint
      📄 style.css
DANGER

DO NOT put files related to an entrypoint directly inside the entrypoints/ directory. WXT will treat them as entrypoints and try to build them, usually resulting in an error.

Instead, use a directory for that entrypoint:


📂 entrypoints/
   📄 popup.html 
   📄 popup.ts 
   📄 popup.css 
   📂 popup/ 
      📄 index.html 
      📄 main.ts 
      📄 style.css 
Deeply Nested Entrypoints
While the entrypoints/ directory might resemble the pages/ directory of other web frameworks, like Nuxt or Next.js, it does not support deeply nesting entrypoints in the same way.

Entrypoints must be zero or one levels deep for WXT to discover and build them:


📂 entrypoints/
   📂 youtube/ 
       📂 content/ 
          📄 index.ts 
          📄 ... 
       📂 injected/ 
          📄 index.ts 
          📄 ... 
   📂 youtube.content/ 
      📄 index.ts 
      📄 ... 
   📂 youtube-injected/ 
      📄 index.ts 
      📄 ... 
Unlisted Entrypoints
In web extensions, there are two types of entrypoints:

Listed: Referenced in the manifest.json
Unlisted: Not referenced in the manifest.json
Throughout the rest of WXT's documentation, listed entrypoints are referred to by name. For example:

Popup
Options
Background
Content Script
However, not all entrypoints in web extensions are listed in the manifest. Some are not listed in the manifest, but are still used by extensions. For example:

A welcome page shown in a new tab when the extension is installed
JS files injected by content scripts into the main world
For more details on how to add unlisted entrypoints, see:

Unlisted Pages
Unlisted Scripts
Unlisted CSS
Defining Manifest Options
Most listed entrypoints have options that need to be added to the manifest.json. However with WXT, instead of defining the options in a separate file, you define these options inside the entrypoint file itself.

For example, here's how to define matches for content scripts:

entrypoints/content.ts

export default defineContentScript({
  matches: ['*://*.wxt.dev/*'],
  main() {
    // ...
  },
});
For HTML entrypoints, options are configured as <meta> tags. For example, to use a page_action for your MV2 popup:


<!doctype html>
<html lang="en">
  <head>
    <meta name="manifest.type" content="page_action" />
  </head>
</html>
Refer to the Entrypoint Types sections for a list of options configurable inside each entrypoint, and how to define them.

When building your extension, WXT will look at the options defined in your entrypoints, and generate the manifest accordingly.

Entrypoint Types
Background
Chrome Docs • Firefox Docs

Filename		Output Path
entrypoints/background.[jt]s		/background.js
entrypoints/background/index.[jt]s		/background.js

Minimal

With Manifest Options

export default defineBackground(() => {
  // Executed when background is loaded
});
For MV2, the background is added as a script to the background page. For MV3, the background becomes a service worker.

When defining your background entrypoint, keep in mind that WXT will import this file in a NodeJS environment during the build process. That means you cannot place any runtime code outside the main function.


browser.action.onClicked.addListener(() => { 
  // ...
}); 

export default defineBackground(() => {
  browser.action.onClicked.addListener(() => { 
    // ...
  }); 
});
Refer to the Entrypoint Loaders documentation for more details.

Bookmarks
Chrome Docs • Firefox Docs

Filename		Output Path
entrypoints/bookmarks.html		/bookmarks.html
entrypoints/bookmarks/index.html		/bookmarks.html

<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Title</title>
    <!-- Set include/exclude if the page should be removed from some builds -->
    <meta name="manifest.include" content="['chrome', ...]" />
    <meta name="manifest.exclude" content="['chrome', ...]" />
  </head>
  <body>
    <!-- ... -->
  </body>
</html>
When you define a Bookmarks entrypoint, WXT will automatically update the manifest to override the browser's bookmarks page with your own HTML page.

Content Scripts
Chrome Docs • Firefox Docs

Filename		Output Path
entrypoints/content.[jt]sx?		/content-scripts/content.js
entrypoints/content/index.[jt]sx?		/content-scripts/content.js
entrypoints/{name}.content.[jt]sx?		/content-scripts/{name}.js
entrypoints/{name}.content/index.[jt]sx?		/content-scripts/{name}.js

export default defineContentScript({
  // Set manifest options
  matches: string[],
  excludeMatches: undefined | [],
  includeGlobs: undefined | [],
  excludeGlobs: undefined | [],
  allFrames: undefined | true | false,
  runAt: undefined | 'document_start' | 'document_end' | 'document_idle',
  matchAboutBlank: undefined | true | false,
  matchOriginAsFallback: undefined | true | false,
  world: undefined | 'ISOLATED' | 'MAIN',

  // Set include/exclude if the background should be removed from some builds
  include: undefined | string[],
  exclude: undefined | string[],

  // Configure how CSS is injected onto the page
  cssInjectionMode: undefined | "manifest" | "manual" | "ui",

  // Configure how/when content script will be registered
  registration: undefined | "manifest" | "runtime",

  main(ctx: ContentScriptContext) {
    // Executed when content script is loaded, can be async
  },
});
When defining content script entrypoints, keep in mind that WXT will import this file in a NodeJS environment during the build process. That means you cannot place any runtime code outside the main function.


const container = document.createElement('div'); 
document.body.append(container); 

export default defineContentScript({
  main: function () {
    const container = document.createElement('div'); 
    document.body.append(container); 
  },
});
Refer to the Entrypoint Loaders documentation for more details.

See Content Script UI for more info on creating UIs and including CSS in content scripts.

Devtools
Chrome Docs • Firefox Docs

Filename		Output Path
entrypoints/devtools.html		/devtools.html
entrypoints/devtools/index.html		/devtools.html

<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- Set include/exclude if the page should be removed from some builds -->
    <meta name="manifest.include" content="['chrome', ...]" />
    <meta name="manifest.exclude" content="['chrome', ...]" />
  </head>
  <body>
    <!-- ... -->
  </body>
</html>
Follow the Devtools Example to add different panels and panes.

History
Chrome Docs • Firefox Docs

Filename		Output Path
entrypoints/history.html		/history.html
entrypoints/history/index.html		/history.html

<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Title</title>
    <!-- Set include/exclude if the page should be removed from some builds -->
    <meta name="manifest.include" content="['chrome', ...]" />
    <meta name="manifest.exclude" content="['chrome', ...]" />
  </head>
  <body>
    <!-- ... -->
  </body>
</html>
When you define a History entrypoint, WXT will automatically update the manifest to override the browser's history page with your own HTML page.

Newtab
Chrome Docs • Firefox Docs

Filename		Output Path
entrypoints/newtab.html		/newtab.html
entrypoints/newtab/index.html		/newtab.html

<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Title</title>
    <!-- Set include/exclude if the page should be removed from some builds -->
    <meta name="manifest.include" content="['chrome', ...]" />
    <meta name="manifest.exclude" content="['chrome', ...]" />
  </head>
  <body>
    <!-- ... -->
  </body>
</html>
When you define a Newtab entrypoint, WXT will automatically update the manifest to override the browser's new tab page with your own HTML page.

Options
Chrome Docs • Firefox Docs

Filename		Output Path
entrypoints/options.html		/options.html
entrypoints/options/index.html		/options.html

<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Options Title</title>

    <!-- Customize the manifest options -->
    <meta name="manifest.open_in_tab" content="true|false" />
    <meta name="manifest.chrome_style" content="true|false" />
    <meta name="manifest.browser_style" content="true|false" />

    <!-- Set include/exclude if the page should be removed from some builds -->
    <meta name="manifest.include" content="['chrome', ...]" />
    <meta name="manifest.exclude" content="['chrome', ...]" />
  </head>
  <body>
    <!-- ... -->
  </body>
</html>
Popup
Chrome Docs • Firefox Docs

Filename		Output Path
entrypoints/popup.html		/popup.html
entrypoints/popup/index.html		/popup.html

<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Set the `action.default_title` in the manifest -->
    <title>Default Popup Title</title>

    <!-- Customize the manifest options -->
    <meta
      name="manifest.default_icon"
      content="{
        16: '/icon-16.png',
        24: '/icon-24.png',
        ...
      }"
    />
    <meta name="manifest.type" content="page_action|browser_action" />
    <meta name="manifest.browser_style" content="true|false" />

    <!-- Set include/exclude if the page should be removed from some builds -->
    <meta name="manifest.include" content="['chrome', ...]" />
    <meta name="manifest.exclude" content="['chrome', ...]" />
  </head>
  <body>
    <!-- ... -->
  </body>
</html>
Sandbox
Chrome Docs

Chromium Only

Firefox does not support sandboxed pages.

Filename		Output Path
entrypoints/sandbox.html		/sandbox.html
entrypoints/sandbox/index.html		/sandbox.html
entrypoints/{name}.sandbox.html		/{name}.html
entrypoints/{name}.sandbox/index.html		/{name}.html

<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Title</title>

    <!-- Set include/exclude if the page should be removed from some builds -->
    <meta name="manifest.include" content="['chrome', ...]" />
    <meta name="manifest.exclude" content="['chrome', ...]" />
  </head>
  <body>
    <!-- ... -->
  </body>
</html>
Side Panel
Chrome Docs • Firefox Docs

Filename		Output Path
entrypoints/sidepanel.html		/sidepanel.html
entrypoints/sidepanel/index.html		/sidepanel.html
entrypoints/{name}.sidepanel.html		/{name}.html`
entrypoints/{name}.sidepanel/index.html		/{name}.html`

<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Default Side Panel Title</title>

    <!-- Customize the manifest options -->
    <meta
      name="manifest.default_icon"
      content="{
        16: '/icon-16.png',
        24: '/icon-24.png',
        ...
      }"
    />
    <meta name="manifest.open_at_install" content="true|false" />
    <meta name="manifest.browser_style" content="true|false" />

    <!-- Set include/exclude if the page should be removed from some builds -->
    <meta name="manifest.include" content="['chrome', ...]" />
    <meta name="manifest.exclude" content="['chrome', ...]" />
  </head>
  <body>
    <!-- ... -->
  </body>
</html>
In Chrome, side panels use the side_panel API, while Firefox uses the sidebar_action API.

Unlisted CSS
Filename		Output Path
entrypoints/{name}.(css|scss|sass|less|styl|stylus)		/{name}.css
entrypoints/{name}/index.(css|scss|sass|less|styl|stylus)		/{name}.css
entrypoints/content.(css|scss|sass|less|styl|stylus)		/content-scripts/content.css
entrypoints/content/index.(css|scss|sass|less|styl|stylus)		/content-scripts/content.css
entrypoints/{name}.content.(css|scss|sass|less|styl|stylus)		/content-scripts/{name}.css
entrypoints/{name}.content/index.(css|scss|sass|less|styl|stylus)		/content-scripts/{name}.css

body {
  /* ... */
}
Follow Vite's guide to setup your preprocessor of choice: https://vitejs.dev/guide/features.html#css-pre-processors

CSS entrypoints are always unlisted. To add CSS to a content script, see the Content Script docs.

Unlisted Pages
Filename		Output Path
entrypoints/{name}.html		/{name}.html
entrypoints/{name}/index.html		/{name}.html

<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Title</title>

    <!-- Set include/exclude if the page should be removed from some builds -->
    <meta name="manifest.include" content="['chrome', ...]" />
    <meta name="manifest.exclude" content="['chrome', ...]" />
  </head>
  <body>
    <!-- ... -->
  </body>
</html>
At runtime, unlisted pages are accessible at /{name}.html:


const url = browser.runtime.getURL('/{name}.html');

console.log(url); // "chrome-extension://{id}/{name}.html"
window.open(url); // Open the page in a new tab
Unlisted Scripts
Filename		Output Path
entrypoints/{name}.[jt]sx?		/{name}.js
entrypoints/{name}/index.[jt]sx?		/{name}.js

Minimal

With Options

export default defineUnlistedScript(() => {
  // Executed when script is loaded
});
At runtime, unlisted scripts are accessible from /{name}.js:


const url = browser.runtime.getURL('/{name}.js');

console.log(url); // "chrome-extension://{id}/{name}.js"
You are responsible for loading/running these scripts where needed. If necessary, don't forget to add the script and/or any related assets to web_accessible_resources.

When defining an unlisted script, keep in mind that WXT will import this file in a NodeJS environment during the build process. That means you cannot place any runtime code outside the main function.


document.querySelectorAll('a').forEach((anchor) => { 
  // ...
}); 

export default defineUnlistedScript(() => {
  document.querySelectorAll('a').forEach((anchor) => { 
    // ...
  }); 
});
Refer to the Entrypoint Loaders documentation for more details.

Edit this page

Manifest
In WXT, there is no manifest.json file in your source code. Instead, WXT generates the manifest from multiple sources:

Global options defined in your wxt.config.ts file
Entrypoint-specific options defined in your entrypoints
WXT Modules added to your project can modify your manifest
Hooks defined in your project can modify your manifest
Your extension's manifest.json will be output to .output/{target}/manifest.json when running wxt build.

Global Options
To add a property to your manifest, use the manifest config inside your wxt.config.ts:


export default defineConfig({
  manifest: {
    // Put manual changes here
  },
});
You can also define the manifest as a function, and use JS to generate it based on the target browser, mode, and more.


export default defineConfig({
  manifest: ({ browser, manifestVersion, mode, command }) => {
    return {
      // ...
    };
  },
});
MV2 and MV3 Compatibility
When adding properties to the manifest, always define the property in it's MV3 format when possible. When targeting MV2, WXT will automatically convert these properties to their MV2 format.

For example, for this config:


export default defineConfig({
  manifest: {
    action: {
      default_title: 'Some Title',
    },
    web_accessible_resources: [
      {
        matches: ['*://*.google.com/*'],
        resources: ['icon/*.png'],
      },
    ],
  },
});
WXT will generate the following manifests:


MV2

MV3

{
  "manifest_version": 2,
  // ...
  "browser_action": {
    "default_title": "Some Title"
  },
  "web_accessible_resources": ["icon/*.png"]
}
You can also specify properties specific to a single manifest version, and they will be stripped out when targeting the other manifest version.

Name
Chrome Docs

If not provided via the manifest config, the manifest's name property defaults to your package.json's name property.

Version and Version Name
Chrome Docs

Your extension's version and version_name is based on the version from your package.json.

version_name is the exact string listed
version is the string cleaned up, with any invalid suffixes removed
Example:


// package.json
{
  "version": "1.3.0-alpha2"
}

// .output/<target>/manifest.json
{
  "version": "1.3.0",
  "version_name": "1.3.0-alpha2"
}
If a version is not present in your package.json, it defaults to "0.0.0".

Icons
WXT automatically discovers your extension's icon by looking at files in the public/ directory:


public/
├─ icon-16.png
├─ icon-24.png
├─ icon-48.png
├─ icon-96.png
└─ icon-128.png
Specifically, an icon must match one of these regex to be discovered:


const iconRegex = [
  /^icon-([0-9]+)\.png$/,                 // icon-16.png
  /^icon-([0-9]+)x[0-9]+\.png$/,          // icon-16x16.png
  /^icon@([0-9]+)w\.png$/,                // <EMAIL>
  /^icon@([0-9]+)h\.png$/,                // <EMAIL>
  /^icon@([0-9]+)\.png$/,                 // <EMAIL>
  /^icons?[/\\]([0-9]+)\.png$/,          // icon/16.png | icons/16.png
  /^icons?[/\\]([0-9]+)x[0-9]+\.png$/,   // icon/16x16.png | icons/16x16.png
];
If you don't like these filename or you're migrating to WXT and don't want to rename the files, you can manually specify an icon in your manifest:


export default defineConfig({
  manifest: {
    icons: {
      16: '/extension-icon-16.png',
      24: '/extension-icon-24.png',
      48: '/extension-icon-48.png',
      96: '/extension-icon-96.png',
      128: '/extension-icon-128.png',
    },
  },
});
Alternatively, you can use @wxt-dev/auto-icons to let WXT generate your icon at the required sizes.

Permissions
Chrome docs

Most of the time, you need to manually add permissions to your manifest. Only in a few specific situations are permissions added automatically:

During development: the tabs and scripting permissions will be added to enable hot reloading.
When a sidepanel entrypoint is present: The sidepanel permission is added.

export default defineConfig({
  manifest: {
    permissions: ['storage', 'tabs'],
  },
});
Host Permissions
Chrome docs


export default defineConfig({
  manifest: {
    host_permissions: ['https://www.google.com/*'],
  },
});
WARNING

If you use host permissions and target both MV2 and MV3, make sure to only include the required host permissions for each version:


export default defineConfig({
  manifest: ({ manifestVersion }) => ({
    host_permissions: manifestVersion === 2 ? [...] : [...],
  }),
});
Default Locale

export default defineConfig({
  manifest: {
    name: '__MSG_extName__',
    description: '__MSG_extDescription__',
    default_locale: 'en',
  },
});
See I18n docs for a full guide on internationalizing your extension.

Actions
In MV2, you have two options: browser_action and page_action. In MV3, they were merged into a single action API.

By default, whenever an action is generated, WXT falls back to browser_action when targeting MV2.

Action With Popup
To generate a manifest where a UI appears after clicking the icon, just create a Popup entrypoint. If you want to use a page_action for MV2, add the following meta tag to the HTML document's head:


<meta name="manifest.type" content="page_action" />
Action Without Popup
If you want to use the activeTab permission or the browser.action.onClicked event, but don't want to show a popup:

Delete the Popup entrypoint if it exists

Add the action key to your manifest:


export default defineConfig({
  manifest: {
    action: {},
  },
});
Same as an action with a popup, WXT will fallback on using browser_action for MV2. To use a page_action instead, add that key as well:


export default defineConfig({
  manifest: {
    action: {},
    page_action: {},
  },
});
Edit this page
Last updated: 04/06/2025, 16:30

Pager
Previous page