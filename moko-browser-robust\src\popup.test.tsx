import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react';
import { vi } from 'vitest';
import Popup from './popup';

// Mock chrome APIs
const mockChromeTabs = {
  update: vi.fn(),
  create: vi.fn(),
  query: vi.fn(),
};

const mockChromeRuntime = {
  onMessage: {
    addListener: vi.fn(),
    removeListener: vi.fn(),
  },
  sendMessage: vi.fn(() => Promise.resolve({})),
};

Object.defineProperty(global, 'chrome', {
  value: {
    tabs: mockChromeTabs,
    runtime: mockChromeRuntime,
  },
  writable: true,
});

describe('Popup Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Ensure the root element exists for testing-library's render
    document.body.innerHTML = '<div id="root"></div>';
  });

  it('renders correctly', () => {
    render(<Popup />);
    expect(screen.getByText('Moko Browser')).toBeDefined();
    expect(screen.getByPlaceholderText('Enter URL or search query')).toBeDefined();
    expect(screen.getByText('Go / Search')).toBeDefined();
    expect(screen.getByText('Get Current URL')).toBeDefined();
  });

  it('handles URL navigation', () => {
    render(<Popup />);
    const input = screen.getByPlaceholderText('Enter URL or search query');
    const goSearchButton = screen.getByText('Go / Search');

    fireEvent.change(input, { target: { value: 'https://example.com' } });
    fireEvent.click(goSearchButton);

    expect(mockChromeTabs.update).toHaveBeenCalledWith({ url: 'https://example.com' });
  });

  it('handles search query', () => {
    render(<Popup />);
    const input = screen.getByPlaceholderText('Enter URL or search query');
    const goSearchButton = screen.getByText('Go / Search');

    fireEvent.change(input, { target: { value: 'test search' } });
    fireEvent.click(goSearchButton);

    expect(mockChromeTabs.create).toHaveBeenCalledWith({ url: 'https://www.google.com/search?q=test%20search' });
  });

  it('displays current URL', async () => {
    const mockUrl = 'https://current-page.com';
    mockChromeTabs.query.mockImplementationOnce((queryInfo, callback) => {
      callback([{ url: mockUrl }]);
    });

    render(<Popup />);
    const getCurrentUrlButton = screen.getByText('Get Current URL');
    fireEvent.click(getCurrentUrlButton);

    const linkElement = await screen.findByRole('link', { name: mockUrl });
    expect(linkElement).toHaveAttribute('href', mockUrl);
  });
});
