Comprehensive App Store Inspection Checklist
For Google Play Store & Apple App Store Approval

This master checklist covers the technical, policy, and documentation requirements your app must meet—ensuring the highest chances of passing initial review for both Android (Google Play) and iOS (Apple App Store). Use this before white labeling or mass deployment.

1. General App Quality & Functionality
App does not crash or freeze during normal use (test all user flows)

All screens and features are responsive, with no blank screens or unhandled errors

No placeholder content, “lorem ipsum,” or unfinished UI

App supports modern devices: different screen sizes, notch/cutout, and orientation (portrait/landscape if applicable)

Smooth launch sequence (logo, splash screen, then home/main UI)

Optimized for battery, memory, network, and startup (no excessive background activity)

2. Privacy, Security, and Data Compliance
Privacy Policy: Clearly available within app and linked in store listing (in-app link mandatory)

User Data: Only collects data necessary for core features; fully discloses data use in the policy

Permissions: Requests only the permissions needed for functionality (camera, mic, location, etc.); just-in-time prompts, not all at first launch

Secure Data Storage: No plain-text passwords, secure storage for sensitive info, HTTPS for all data transfers

Tracking/Ads Disclosure: Informs users of any analytics, ad networks, or tracking; conforms to privacy frameworks (ATT on iOS, Data Safety on Google)

Account Deletion: If sign-up offered, provides clear and easy in-app account deletion (Apple requirement)

COPPA/Child Safety: If serving users under 13/16, full compliance with children’s privacy laws

3. Content & User-Generated Data
Content Moderation: Process for reporting/blocking offensive content or users (works for chat, media uploads, etc.)

Community Guidelines: Terms of use/code of conduct accessible from app

No Illegal or Infringing Content: Filters or moderation steps for copyrighted music, media, hate speech, or illegal activity

4. User Interface & Accessibility
Consistent navigation (tab bar, hamburger menu, etc.), no dead ends

All text legible, sufficient contrast, accessible fonts/sizes

Touch targets large enough; UI not overcrowded or too small on any device

Accessibility: Supports screen readers/VoiceOver, focus order, alt text for images

Localized for core languages/regions as needed

5. Monetization & Payment (If Used)
Google In-App Billing / Apple In-App Purchase: All digital goods, subscriptions, and tips use official APIs only

Clear pricing, subscription details, refund/cancellation mechanisms

“Restore Purchases” option for iOS

No misleading purchase prompts, no “loot boxes” targeting minors without disclosure

6. App Store Listing Assets & Metadata
Unique app name, no keywords or competitor names

Clear and non-misleading app description

High-quality screenshots (per device size), app icon, and (if submitted) a preview video

No mention of other platforms, test/beta status, or unrelated features in the listing

All required keyword/search metadata provided relevantly

7. Technical & Platform Requirements
Category	Android (Google Play)	iOS (Apple App Store)
API Level	Targets latest stable API (targetSdkVersion)	Supports latest and at least 1–2 prior iOS versions
App Bundling	Uses Android App Bundle (.aab)	.ipa via Xcode Archive, bitcode if required
64-bit Support	Required for all new Android apps	Required
Push Notifications	Configured through FCM (Android), APNs (iOS)	Uses system APIs and user permission prompts
Device Permissions	Uses runtime permission flow, explains use	Includes NS*UsageDescription entries in Info.plist
8. App Store Policy Compliance
No third-party app store, sideloading, or “downloader” features

No instructions on bypassing platform restrictions

Does not mimic or copy system apps/UI excessively

No gambling (unless licensed and regionally legal)

No private or restricted APIs used

9. Advanced & Platform-Specific Checks
App Transport Security (iOS): All network calls use HTTPS or are whitelisted with documented justification

Data Safety Section (Google): Play Console Data Safety form accurately and honestly completed

App Tracking Transparency (ATT/iOS): ATT dialog and flow if any ad or user tracking present

10. QA/Test Checklist Before Submission
Test login, sign up, and onboarding (with and without network)

Verify all payment/tipping/subscription flows

Upload/stream test media, confirm moderation/reporting UI

Regression test for crashes, “back” button handling, orientation changes

Check uninstall/reinstall, and lost network/cloud sync scenarios

Run automated device testing/CI (if possible)

Final Submission Checklist Table
Item	Google Play	App Store
Privacy Policy	✓	✓
Data License/Usage	✓	✓
App Icons/Screenshots	✓	✓
Latest SDK/API Level	✓	✓
Functional deep links	✓	✓
In-app purchase compliance	✓	✓
User account deletion	✓	✓
Accessibility	✓	✓
Crash/log error-free	✓	✓
Content filtering/moderation	✓	✓
What to Include in Your Internal Inspection
Print and review this checklist for each submission.

Perform “test case walkthroughs” for every user flow.

Simulate rejection scenarios: test with incomplete data, invalid permissions, non-compliant content.

Address every warning/error from Google Play Console and Apple’s TestFlight/Xcode validation before submission.

Keep records/screenshots of compliant flows for your white label/client partners.

Using this checklist maximizes your approval chances with both stores and sets a standard for rigorous, repeatable pre-deployment QA—essential for every enterprise or scalable white-label rollout.