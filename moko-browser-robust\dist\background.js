/*! For license information please see background.js.LICENSE.txt */
(()=>{"use strict";var t,e,n;!function(t){t.STRING="string",t.NUMBER="number",t.INTEGER="integer",t.BOOLEAN="boolean",t.ARRAY="array",t.OBJECT="object"}(t||(t={})),function(t){t.LANGUAGE_UNSPECIFIED="language_unspecified",t.PYTHON="python"}(e||(e={})),function(t){t.OUTCOME_UNSPECIFIED="outcome_unspecified",t.OUTCOME_OK="outcome_ok",t.OUTCOME_FAILED="outcome_failed",t.OUTCOME_DEADLINE_EXCEEDED="outcome_deadline_exceeded"}(n||(n={}));const o=["user","model","function","system"];var s,i,r,a,c,u,l,d,f;!function(t){t.HARM_CATEGORY_UNSPECIFIED="HARM_CATEGORY_UNSPECIFIED",t.HARM_CATEGORY_HATE_SPEECH="HARM_CATEGORY_HATE_SPEECH",t.HARM_CATEGORY_SEXUALLY_EXPLICIT="HARM_CATEGORY_SEXUALLY_EXPLICIT",t.HARM_CATEGORY_HARASSMENT="HARM_CATEGORY_HARASSMENT",t.HARM_CATEGORY_DANGEROUS_CONTENT="HARM_CATEGORY_DANGEROUS_CONTENT",t.HARM_CATEGORY_CIVIC_INTEGRITY="HARM_CATEGORY_CIVIC_INTEGRITY"}(s||(s={})),function(t){t.HARM_BLOCK_THRESHOLD_UNSPECIFIED="HARM_BLOCK_THRESHOLD_UNSPECIFIED",t.BLOCK_LOW_AND_ABOVE="BLOCK_LOW_AND_ABOVE",t.BLOCK_MEDIUM_AND_ABOVE="BLOCK_MEDIUM_AND_ABOVE",t.BLOCK_ONLY_HIGH="BLOCK_ONLY_HIGH",t.BLOCK_NONE="BLOCK_NONE"}(i||(i={})),function(t){t.HARM_PROBABILITY_UNSPECIFIED="HARM_PROBABILITY_UNSPECIFIED",t.NEGLIGIBLE="NEGLIGIBLE",t.LOW="LOW",t.MEDIUM="MEDIUM",t.HIGH="HIGH"}(r||(r={})),function(t){t.BLOCKED_REASON_UNSPECIFIED="BLOCKED_REASON_UNSPECIFIED",t.SAFETY="SAFETY",t.OTHER="OTHER"}(a||(a={})),function(t){t.FINISH_REASON_UNSPECIFIED="FINISH_REASON_UNSPECIFIED",t.STOP="STOP",t.MAX_TOKENS="MAX_TOKENS",t.SAFETY="SAFETY",t.RECITATION="RECITATION",t.LANGUAGE="LANGUAGE",t.BLOCKLIST="BLOCKLIST",t.PROHIBITED_CONTENT="PROHIBITED_CONTENT",t.SPII="SPII",t.MALFORMED_FUNCTION_CALL="MALFORMED_FUNCTION_CALL",t.OTHER="OTHER"}(c||(c={})),function(t){t.TASK_TYPE_UNSPECIFIED="TASK_TYPE_UNSPECIFIED",t.RETRIEVAL_QUERY="RETRIEVAL_QUERY",t.RETRIEVAL_DOCUMENT="RETRIEVAL_DOCUMENT",t.SEMANTIC_SIMILARITY="SEMANTIC_SIMILARITY",t.CLASSIFICATION="CLASSIFICATION",t.CLUSTERING="CLUSTERING"}(u||(u={})),function(t){t.MODE_UNSPECIFIED="MODE_UNSPECIFIED",t.AUTO="AUTO",t.ANY="ANY",t.NONE="NONE"}(l||(l={})),function(t){t.MODE_UNSPECIFIED="MODE_UNSPECIFIED",t.MODE_DYNAMIC="MODE_DYNAMIC"}(d||(d={}));class h extends Error{constructor(t){super(`[GoogleGenerativeAI Error]: ${t}`)}}class p extends h{constructor(t,e){super(t),this.response=e}}class g extends h{constructor(t,e,n,o){super(t),this.status=e,this.statusText=n,this.errorDetails=o}}class m extends h{}class E extends h{}!function(t){t.GENERATE_CONTENT="generateContent",t.STREAM_GENERATE_CONTENT="streamGenerateContent",t.COUNT_TOKENS="countTokens",t.EMBED_CONTENT="embedContent",t.BATCH_EMBED_CONTENTS="batchEmbedContents"}(f||(f={}));class C{constructor(t,e,n,o,s){this.model=t,this.task=e,this.apiKey=n,this.stream=o,this.requestOptions=s}toString(){var t,e;const n=(null===(t=this.requestOptions)||void 0===t?void 0:t.apiVersion)||"v1beta";let o=`${(null===(e=this.requestOptions)||void 0===e?void 0:e.baseUrl)||"https://generativelanguage.googleapis.com"}/${n}/${this.model}:${this.task}`;return this.stream&&(o+="?alt=sse"),o}}async function v(t){var e;const n=new Headers;n.append("Content-Type","application/json"),n.append("x-goog-api-client",function(t){const e=[];return(null==t?void 0:t.apiClient)&&e.push(t.apiClient),e.push("genai-js/0.24.1"),e.join(" ")}(t.requestOptions)),n.append("x-goog-api-key",t.apiKey);let o=null===(e=t.requestOptions)||void 0===e?void 0:e.customHeaders;if(o){if(!(o instanceof Headers))try{o=new Headers(o)}catch(t){throw new m(`unable to convert customHeaders value ${JSON.stringify(o)} to Headers: ${t.message}`)}for(const[t,e]of o.entries()){if("x-goog-api-key"===t)throw new m(`Cannot set reserved header name ${t}`);if("x-goog-api-client"===t)throw new m(`Header name ${t} can only be set using the apiClient field`);n.append(t,e)}}return n}async function y(t,e,n,o,s,i={},r=fetch){const{url:a,fetchOptions:c}=await async function(t,e,n,o,s,i){const r=new C(t,e,n,o,i);return{url:r.toString(),fetchOptions:Object.assign(Object.assign({},O(i)),{method:"POST",headers:await v(r),body:s})}}(t,e,n,o,s,i);return async function(t,e,n=fetch){let o;try{o=await n(t,e)}catch(e){!function(t,e){let n=t;throw"AbortError"===n.name?(n=new E(`Request aborted when fetching ${e.toString()}: ${t.message}`),n.stack=t.stack):t instanceof g||t instanceof m||(n=new h(`Error fetching from ${e.toString()}: ${t.message}`),n.stack=t.stack),n}(e,t)}return o.ok||await async function(t,e){let n,o="";try{const e=await t.json();o=e.error.message,e.error.details&&(o+=` ${JSON.stringify(e.error.details)}`,n=e.error.details)}catch(t){}throw new g(`Error fetching from ${e.toString()}: [${t.status} ${t.statusText}] ${o}`,t.status,t.statusText,n)}(o,t),o}(a,c,r)}function O(t){const e={};if(void 0!==(null==t?void 0:t.signal)||(null==t?void 0:t.timeout)>=0){const n=new AbortController;(null==t?void 0:t.timeout)>=0&&setTimeout(()=>n.abort(),t.timeout),(null==t?void 0:t.signal)&&t.signal.addEventListener("abort",()=>{n.abort()}),e.signal=n.signal}return e}function _(t){return t.text=()=>{if(t.candidates&&t.candidates.length>0){if(t.candidates.length>1&&console.warn(`This response had ${t.candidates.length} candidates. Returning text from the first candidate only. Access response.candidates directly to use the other candidates.`),b(t.candidates[0]))throw new p(`${w(t)}`,t);return function(t){var e,n,o,s;const i=[];if(null===(n=null===(e=t.candidates)||void 0===e?void 0:e[0].content)||void 0===n?void 0:n.parts)for(const e of null===(s=null===(o=t.candidates)||void 0===o?void 0:o[0].content)||void 0===s?void 0:s.parts)e.text&&i.push(e.text),e.executableCode&&i.push("\n```"+e.executableCode.language+"\n"+e.executableCode.code+"\n```\n"),e.codeExecutionResult&&i.push("\n```\n"+e.codeExecutionResult.output+"\n```\n");return i.length>0?i.join(""):""}(t)}if(t.promptFeedback)throw new p(`Text not available. ${w(t)}`,t);return""},t.functionCall=()=>{if(t.candidates&&t.candidates.length>0){if(t.candidates.length>1&&console.warn(`This response had ${t.candidates.length} candidates. Returning function calls from the first candidate only. Access response.candidates directly to use the other candidates.`),b(t.candidates[0]))throw new p(`${w(t)}`,t);return console.warn("response.functionCall() is deprecated. Use response.functionCalls() instead."),I(t)[0]}if(t.promptFeedback)throw new p(`Function call not available. ${w(t)}`,t)},t.functionCalls=()=>{if(t.candidates&&t.candidates.length>0){if(t.candidates.length>1&&console.warn(`This response had ${t.candidates.length} candidates. Returning function calls from the first candidate only. Access response.candidates directly to use the other candidates.`),b(t.candidates[0]))throw new p(`${w(t)}`,t);return I(t)}if(t.promptFeedback)throw new p(`Function call not available. ${w(t)}`,t)},t}function I(t){var e,n,o,s;const i=[];if(null===(n=null===(e=t.candidates)||void 0===e?void 0:e[0].content)||void 0===n?void 0:n.parts)for(const e of null===(s=null===(o=t.candidates)||void 0===o?void 0:o[0].content)||void 0===s?void 0:s.parts)e.functionCall&&i.push(e.functionCall);return i.length>0?i:void 0}const T=[c.RECITATION,c.SAFETY,c.LANGUAGE];function b(t){return!!t.finishReason&&T.includes(t.finishReason)}function w(t){var e,n,o;let s="";if(t.candidates&&0!==t.candidates.length||!t.promptFeedback){if(null===(o=t.candidates)||void 0===o?void 0:o[0]){const e=t.candidates[0];b(e)&&(s+=`Candidate was blocked due to ${e.finishReason}`,e.finishMessage&&(s+=`: ${e.finishMessage}`))}}else s+="Response was blocked",(null===(e=t.promptFeedback)||void 0===e?void 0:e.blockReason)&&(s+=` due to ${t.promptFeedback.blockReason}`),(null===(n=t.promptFeedback)||void 0===n?void 0:n.blockReasonMessage)&&(s+=`: ${t.promptFeedback.blockReasonMessage}`);return s}function N(t){return this instanceof N?(this.v=t,this):new N(t)}"function"==typeof SuppressedError&&SuppressedError;const A=/^data\: (.*)(?:\n\n|\r\r|\r\n\r\n)/;async function S(t){const e=[],n=t.getReader();for(;;){const{done:t,value:o}=await n.read();if(t)return _(M(e));e.push(o)}}function R(t){return function(t,e,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var o,s=n.apply(t,e||[]),i=[];return o={},r("next"),r("throw"),r("return"),o[Symbol.asyncIterator]=function(){return this},o;function r(t){s[t]&&(o[t]=function(e){return new Promise(function(n,o){i.push([t,e,n,o])>1||a(t,e)})})}function a(t,e){try{(n=s[t](e)).value instanceof N?Promise.resolve(n.value.v).then(c,u):l(i[0][2],n)}catch(t){l(i[0][3],t)}var n}function c(t){a("next",t)}function u(t){a("throw",t)}function l(t,e){t(e),i.shift(),i.length&&a(i[0][0],i[0][1])}}(this,arguments,function*(){const e=t.getReader();for(;;){const{value:t,done:n}=yield N(e.read());if(n)break;yield yield N(_(t))}})}function M(t){const e=t[t.length-1],n={promptFeedback:null==e?void 0:e.promptFeedback};for(const e of t){if(e.candidates){let t=0;for(const o of e.candidates)if(n.candidates||(n.candidates=[]),n.candidates[t]||(n.candidates[t]={index:t}),n.candidates[t].citationMetadata=o.citationMetadata,n.candidates[t].groundingMetadata=o.groundingMetadata,n.candidates[t].finishReason=o.finishReason,n.candidates[t].finishMessage=o.finishMessage,n.candidates[t].safetyRatings=o.safetyRatings,o.content&&o.content.parts){n.candidates[t].content||(n.candidates[t].content={role:o.content.role||"user",parts:[]});const e={};for(const s of o.content.parts)s.text&&(e.text=s.text),s.functionCall&&(e.functionCall=s.functionCall),s.executableCode&&(e.executableCode=s.executableCode),s.codeExecutionResult&&(e.codeExecutionResult=s.codeExecutionResult),0===Object.keys(e).length&&(e.text=""),n.candidates[t].content.parts.push(e)}t++}e.usageMetadata&&(n.usageMetadata=e.usageMetadata)}return n}async function x(t,e,n,o){return function(t){const e=function(t){const e=t.getReader();return new ReadableStream({start(t){let n="";return function o(){return e.read().then(({value:e,done:s})=>{if(s)return n.trim()?void t.error(new h("Failed to parse stream")):void t.close();n+=e;let i,r=n.match(A);for(;r;){try{i=JSON.parse(r[1])}catch(e){return void t.error(new h(`Error parsing JSON response: "${r[1]}"`))}t.enqueue(i),n=n.substring(r[0].length),r=n.match(A)}return o()}).catch(t=>{let e=t;throw e.stack=t.stack,e="AbortError"===e.name?new E("Request aborted when reading from the stream"):new h("Error reading from the stream"),e})}()}})}(t.body.pipeThrough(new TextDecoderStream("utf8",{fatal:!0}))),[n,o]=e.tee();return{stream:R(n),response:S(o)}}(await y(e,f.STREAM_GENERATE_CONTENT,t,!0,JSON.stringify(n),o))}async function L(t,e,n,o){const s=await y(e,f.GENERATE_CONTENT,t,!1,JSON.stringify(n),o);return{response:_(await s.json())}}function D(t){if(null!=t)return"string"==typeof t?{role:"system",parts:[{text:t}]}:t.text?{role:"system",parts:[t]}:t.parts?t.role?t:{role:"system",parts:t.parts}:void 0}function P(t){let e=[];if("string"==typeof t)e=[{text:t}];else for(const n of t)"string"==typeof n?e.push({text:n}):e.push(n);return function(t){const e={role:"user",parts:[]},n={role:"function",parts:[]};let o=!1,s=!1;for(const i of t)"functionResponse"in i?(n.parts.push(i),s=!0):(e.parts.push(i),o=!0);if(o&&s)throw new h("Within a single message, FunctionResponse cannot be mixed with other type of part in the request for sending chat message.");if(!o&&!s)throw new h("No content is provided for sending chat message.");return o?e:n}(e)}function G(t){let e;return e=t.contents?t:{contents:[P(t)]},t.systemInstruction&&(e.systemInstruction=D(t.systemInstruction)),e}const k=["text","inlineData","functionCall","functionResponse","executableCode","codeExecutionResult"],j={user:["text","inlineData"],function:["functionResponse"],model:["text","functionCall","executableCode","codeExecutionResult"],system:["text"]};function F(t){var e;if(void 0===t.candidates||0===t.candidates.length)return!1;const n=null===(e=t.candidates[0])||void 0===e?void 0:e.content;if(void 0===n)return!1;if(void 0===n.parts||0===n.parts.length)return!1;for(const t of n.parts){if(void 0===t||0===Object.keys(t).length)return!1;if(void 0!==t.text&&""===t.text)return!1}return!0}const H="SILENT_ERROR";class U{constructor(t,e,n,s={}){this.model=e,this.params=n,this._requestOptions=s,this._history=[],this._sendPromise=Promise.resolve(),this._apiKey=t,(null==n?void 0:n.history)&&(function(t){let e=!1;for(const n of t){const{role:t,parts:s}=n;if(!e&&"user"!==t)throw new h(`First content should be with role 'user', got ${t}`);if(!o.includes(t))throw new h(`Each item should include role field. Got ${t} but valid roles are: ${JSON.stringify(o)}`);if(!Array.isArray(s))throw new h("Content should have 'parts' property with an array of Parts");if(0===s.length)throw new h("Each Content should have at least one part");const i={text:0,inlineData:0,functionCall:0,functionResponse:0,fileData:0,executableCode:0,codeExecutionResult:0};for(const t of s)for(const e of k)e in t&&(i[e]+=1);const r=j[t];for(const e of k)if(!r.includes(e)&&i[e]>0)throw new h(`Content with role '${t}' can't contain '${e}' part`);e=!0}}(n.history),this._history=n.history)}async getHistory(){return await this._sendPromise,this._history}async sendMessage(t,e={}){var n,o,s,i,r,a;await this._sendPromise;const c=P(t),u={safetySettings:null===(n=this.params)||void 0===n?void 0:n.safetySettings,generationConfig:null===(o=this.params)||void 0===o?void 0:o.generationConfig,tools:null===(s=this.params)||void 0===s?void 0:s.tools,toolConfig:null===(i=this.params)||void 0===i?void 0:i.toolConfig,systemInstruction:null===(r=this.params)||void 0===r?void 0:r.systemInstruction,cachedContent:null===(a=this.params)||void 0===a?void 0:a.cachedContent,contents:[...this._history,c]},l=Object.assign(Object.assign({},this._requestOptions),e);let d;return this._sendPromise=this._sendPromise.then(()=>L(this._apiKey,this.model,u,l)).then(t=>{var e;if(F(t.response)){this._history.push(c);const n=Object.assign({parts:[],role:"model"},null===(e=t.response.candidates)||void 0===e?void 0:e[0].content);this._history.push(n)}else{const e=w(t.response);e&&console.warn(`sendMessage() was unsuccessful. ${e}. Inspect response object for details.`)}d=t}).catch(t=>{throw this._sendPromise=Promise.resolve(),t}),await this._sendPromise,d}async sendMessageStream(t,e={}){var n,o,s,i,r,a;await this._sendPromise;const c=P(t),u={safetySettings:null===(n=this.params)||void 0===n?void 0:n.safetySettings,generationConfig:null===(o=this.params)||void 0===o?void 0:o.generationConfig,tools:null===(s=this.params)||void 0===s?void 0:s.tools,toolConfig:null===(i=this.params)||void 0===i?void 0:i.toolConfig,systemInstruction:null===(r=this.params)||void 0===r?void 0:r.systemInstruction,cachedContent:null===(a=this.params)||void 0===a?void 0:a.cachedContent,contents:[...this._history,c]},l=Object.assign(Object.assign({},this._requestOptions),e),d=x(this._apiKey,this.model,u,l);return this._sendPromise=this._sendPromise.then(()=>d).catch(t=>{throw new Error(H)}).then(t=>t.response).then(t=>{if(F(t)){this._history.push(c);const e=Object.assign({},t.candidates[0].content);e.role||(e.role="model"),this._history.push(e)}else{const e=w(t);e&&console.warn(`sendMessageStream() was unsuccessful. ${e}. Inspect response object for details.`)}}).catch(t=>{t.message!==H&&console.error(t)}),d}}class ${constructor(t,e,n={}){this.apiKey=t,this._requestOptions=n,e.model.includes("/")?this.model=e.model:this.model=`models/${e.model}`,this.generationConfig=e.generationConfig||{},this.safetySettings=e.safetySettings||[],this.tools=e.tools,this.toolConfig=e.toolConfig,this.systemInstruction=D(e.systemInstruction),this.cachedContent=e.cachedContent}async generateContent(t,e={}){var n;const o=G(t),s=Object.assign(Object.assign({},this._requestOptions),e);return L(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:null===(n=this.cachedContent)||void 0===n?void 0:n.name},o),s)}async generateContentStream(t,e={}){var n;const o=G(t),s=Object.assign(Object.assign({},this._requestOptions),e);return x(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:null===(n=this.cachedContent)||void 0===n?void 0:n.name},o),s)}startChat(t){var e;return new U(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:null===(e=this.cachedContent)||void 0===e?void 0:e.name},t),this._requestOptions)}async countTokens(t,e={}){const n=function(t,e){var n;let o={model:null==e?void 0:e.model,generationConfig:null==e?void 0:e.generationConfig,safetySettings:null==e?void 0:e.safetySettings,tools:null==e?void 0:e.tools,toolConfig:null==e?void 0:e.toolConfig,systemInstruction:null==e?void 0:e.systemInstruction,cachedContent:null===(n=null==e?void 0:e.cachedContent)||void 0===n?void 0:n.name,contents:[]};const s=null!=t.generateContentRequest;if(t.contents){if(s)throw new m("CountTokensRequest must have one of contents or generateContentRequest, not both.");o.contents=t.contents}else if(s)o=Object.assign(Object.assign({},o),t.generateContentRequest);else{const e=P(t);o.contents=[e]}return{generateContentRequest:o}}(t,{model:this.model,generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:this.cachedContent}),o=Object.assign(Object.assign({},this._requestOptions),e);return async function(t,e,n,o){return(await y(e,f.COUNT_TOKENS,t,!1,JSON.stringify(n),o)).json()}(this.apiKey,this.model,n,o)}async embedContent(t,e={}){const n="string"==typeof(s=t)||Array.isArray(s)?{content:P(s)}:s,o=Object.assign(Object.assign({},this._requestOptions),e);var s;return async function(t,e,n,o){return(await y(e,f.EMBED_CONTENT,t,!1,JSON.stringify(n),o)).json()}(this.apiKey,this.model,n,o)}async batchEmbedContents(t,e={}){const n=Object.assign(Object.assign({},this._requestOptions),e);return async function(t,e,n,o){const s=n.requests.map(t=>Object.assign(Object.assign({},t),{model:e}));return(await y(e,f.BATCH_EMBED_CONTENTS,t,!1,JSON.stringify({requests:s}),o)).json()}(this.apiKey,this.model,t,n)}}function Y(){var t,e,n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",s=n.toStringTag||"@@toStringTag";function i(n,o,s,i){var c=o&&o.prototype instanceof a?o:a,u=Object.create(c.prototype);return B(u,"_invoke",function(n,o,s){var i,a,c,u=0,l=s||[],d=!1,f={p:0,n:0,v:t,a:h,f:h.bind(t,4),d:function(e,n){return i=e,a=0,c=t,f.n=n,r}};function h(n,o){for(a=n,c=o,e=0;!d&&u&&!s&&e<l.length;e++){var s,i=l[e],h=f.p,p=i[2];n>3?(s=p===o)&&(c=i[(a=i[4])?5:(a=3,3)],i[4]=i[5]=t):i[0]<=h&&((s=n<2&&h<i[1])?(a=0,f.v=o,f.n=i[1]):h<p&&(s=n<3||i[0]>o||o>p)&&(i[4]=n,i[5]=o,f.n=p,a=0))}if(s||n>1)return r;throw d=!0,o}return function(s,l,p){if(u>1)throw TypeError("Generator is already running");for(d&&1===l&&h(l,p),a=l,c=p;(e=a<2?t:c)||!d;){i||(a?a<3?(a>1&&(f.n=-1),h(a,c)):f.n=c:f.v=c);try{if(u=2,i){if(a||(s="next"),e=i[s]){if(!(e=e.call(i,c)))throw TypeError("iterator result is not an object");if(!e.done)return e;c=e.value,a<2&&(a=0)}else 1===a&&(e=i.return)&&e.call(i),a<2&&(c=TypeError("The iterator does not provide a '"+s+"' method"),a=1);i=t}else if((e=(d=f.n<0)?c:n.call(o,f))!==r)break}catch(e){i=t,a=1,c=e}finally{u=1}}return{value:e,done:d}}}(n,s,i),!0),u}var r={};function a(){}function c(){}function u(){}e=Object.getPrototypeOf;var l=[][o]?e(e([][o]())):(B(e={},o,function(){return this}),e),d=u.prototype=a.prototype=Object.create(l);function f(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,u):(t.__proto__=u,B(t,s,"GeneratorFunction")),t.prototype=Object.create(d),t}return c.prototype=u,B(d,"constructor",u),B(u,"constructor",c),c.displayName="GeneratorFunction",B(u,s,"GeneratorFunction"),B(d),B(d,s,"Generator"),B(d,o,function(){return this}),B(d,"toString",function(){return"[object Generator]"}),(Y=function(){return{w:i,m:f}})()}function B(t,e,n,o){var s=Object.defineProperty;try{s({},"",{})}catch(t){s=0}B=function(t,e,n,o){if(e)s?s(t,e,{value:n,enumerable:!o,configurable:!o,writable:!o}):t[e]=n;else{var i=function(e,n){B(t,e,function(t){return this._invoke(e,n,t)})};i("next",0),i("throw",1),i("return",2)}},B(t,e,n,o)}function K(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var o,s,i,r,a=[],c=!0,u=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(a.push(o.value),a.length!==e);c=!0);}catch(t){u=!0,s=t}finally{try{if(!c&&null!=n.return&&(r=n.return(),Object(r)!==r))return}finally{if(u)throw s}}return a}}(t,e)||function(t,e){if(t){if("string"==typeof t)return q(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?q(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function q(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=Array(e);n<e;n++)o[n]=t[n];return o}function J(t,e,n,o,s,i,r){try{var a=t[i](r),c=a.value}catch(t){return void n(t)}a.done?e(c):Promise.resolve(c).then(o,s)}function V(t){return function(){var e=this,n=arguments;return new Promise(function(o,s){var i=t.apply(e,n);function r(t){J(i,o,s,r,a,"next",t)}function a(t){J(i,o,s,r,a,"throw",t)}r(void 0)})}}var W=new class{constructor(t){this.apiKey=t}getGenerativeModel(t,e){if(!t.model)throw new h("Must provide a model name. Example: genai.getGenerativeModel({ model: 'my-model-name' })");return new $(this.apiKey,t,e)}getGenerativeModelFromCachedContent(t,e,n){if(!t.name)throw new m("Cached content must contain a `name` field.");if(!t.model)throw new m("Cached content must contain a `model` field.");const o=["model","systemInstruction"];for(const n of o)if((null==e?void 0:e[n])&&t[n]&&(null==e?void 0:e[n])!==t[n]){if("model"===n&&(e.model.startsWith("models/")?e.model.replace("models/",""):e.model)===(t.model.startsWith("models/")?t.model.replace("models/",""):t.model))continue;throw new m(`Different value for "${n}" specified in modelParams (${e[n]}) and cachedContent (${t[n]})`)}const s=Object.assign(Object.assign({},e),{model:t.model,tools:t.tools,toolConfig:t.toolConfig,systemInstruction:t.systemInstruction,cachedContent:t});return new $(this.apiKey,s,n)}}("AIzaSyDRkj87uewFTRlC4bpg_vXUSNe4I_INMPM");function X(t,e,n){return z.apply(this,arguments)}function z(){return(z=V(Y().m(function t(e,n,o){var s,i,r,a,c,u,l,d;return Y().w(function(t){for(;;)switch(t.p=t.n){case 0:if(!(s=e.toLowerCase()).includes("summarize")||!s.includes("page")){t.n=10;break}if(!n){t.n=9;break}return t.p=1,t.n=2,chrome.tabs.sendMessage(n,{type:"getPageContent"});case 2:if(!(i=t.v)||!i.content){t.n=5;break}return r=i.content,a=W.getGenerativeModel({model:"gemini-pro"}),c="Please summarize the following text from a webpage:\n\n".concat(r.substring(0,1e4),"... (truncated if very long)\n\nSummary:"),t.n=3,a.generateContent(c);case 3:return u=t.v,t.n=4,u.response;case 4:return l=t.v,t.a(2,l.text());case 5:return t.a(2,"Could not get page content for summarization.");case 6:t.n=8;break;case 7:return t.p=7,d=t.v,console.error("Error sending message to content script or summarizing:",d),t.a(2,"Error communicating with page or summarizing content.");case 8:t.n=10;break;case 9:return t.a(2,"Cannot summarize page without an active tab context.");case 10:return t.a(2,null)}},t,null,[[1,7]])}))).apply(this,arguments)}chrome.runtime.onInstalled.addListener(function(){console.log("Moko Browser Robust extension installed.")}),chrome.commands.onCommand.addListener(function(){var t=V(Y().m(function t(e){var n,o,s;return Y().w(function(t){for(;;)switch(t.n){case 0:if("_execute_side_panel_action"!==e){t.n=2;break}return t.n=1,chrome.tabs.query({active:!0,lastFocusedWindow:!0});case 1:if(n=t.v,o=K(n,1),!(s=o[0])||!s.id){t.n=2;break}return t.n=2,chrome.sidePanel.open({tabId:s.id});case 2:return t.a(2)}},t)}));return function(e){return t.apply(this,arguments)}}()),chrome.runtime.onMessage.addListener(function(t,e,n){if("llmRequest"===t.type){var o=function(){var t=V(Y().m(function t(){var o,s,a,c,u,l,d,f,h,p;return Y().w(function(t){for(;;)switch(t.p=t.n){case 0:if(t.p=0,o="",s=[],!r.text){t.n=2;break}return t.n=1,X(r.text,null===(a=e.tab)||void 0===a?void 0:a.id,i);case 1:if(!(c=t.v)){t.n=2;break}return n({success:!0,response:c}),t.a(2);case 2:r.text&&(u=r.text,r.url&&(u+="\n\n(Context URL: ".concat(r.url,")")),r.selectedText&&(u+="\n\n(Selected Text: ".concat(r.selectedText,")")),s.push({text:u})),r.image&&s.push({inlineData:{mimeType:r.image.split(";")[0].split(":")[1],data:r.image.split(",")[1]}}),r.file&&s.push({text:"File content: ".concat(r.file)}),l="gemini-pro"===i&&r.image?W.getGenerativeModel({model:"gemini-pro-vision"}):W.getGenerativeModel({model:"gemini-pro"}),h=i,t.n="gemini-pro"===h?3:"grok"===h?6:"deepseek"===h?7:8;break;case 3:return t.n=4,l.generateContent(s);case 4:return d=t.v,t.n=5,d.response;case 5:return f=t.v,o=f.text(),t.a(3,9);case 6:return o="Grok integration is not yet implemented. Your prompt: ".concat(r.text||"No text provided"),t.a(3,9);case 7:return o="DeepSeek integration is not yet implemented. Your prompt: ".concat(r.text||"No text provided"),t.a(3,9);case 8:o="Unknown model: ".concat(i);case 9:n({success:!0,response:o}),t.n=11;break;case 10:t.p=10,p=t.v,console.error("Error handling LLM request:",p),p instanceof Error?n({success:!1,error:p.message}):n({success:!1,error:"An unknown error occurred."});case 11:return t.a(2)}},t,null,[[0,10]])}));return function(){return t.apply(this,arguments)}}(),s=t,i=s.modelName,r=s.prompt;return o(),!0}})})();