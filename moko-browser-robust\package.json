{"name": "moko-browser-robust", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "webpack --mode production", "watch": "webpack --mode development --watch", "test": "vitest", "typecheck": "tsc --noEmit", "preflight": "npm run build && npm run test && npm run typecheck"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@babel/core": "^7.28.0", "@babel/preset-env": "^7.28.0", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@google/generative-ai": "^0.24.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.7.0", "babel-loader": "^10.0.0", "copy-webpack-plugin": "^13.0.0", "html-webpack-plugin": "^5.6.3", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.8.3", "webpack": "^5.100.2", "webpack-cli": "^6.0.1"}, "devDependencies": {"@types/chrome": "^0.1.1", "vitest": "^3.2.4"}}