// <PERSON><PERSON>rowser AI Assistant Background Script

// API Keys - In production, these should be stored securely
const GEMINI_API_KEY = 'AIzaSyDRkj87uewFTRlC4bpg_vXUSNe4I_INMPM';
const GROK_API_KEY = '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';
const DEEPSEEK_API_KEY = '***********************************';

console.log('<PERSON><PERSON> Browser AI Assistant initialized');

// Handle messages from the sidepanel
chrome.runtime.onMessage.addListener(async (message, sender, sendResponse) => {
  // Handle LLM requests
  if (message.type === 'llmRequest') {
    const { modelName, prompt } = message;
    
    try {
      let responseText = '';
      
      // Process based on the selected model
      switch (modelName) {
        case 'gemini-pro':
          responseText = await callGeminiAPI(prompt);
          break;
          
        case 'grok':
          // Simulate Grok API call (replace with actual API in production)
          responseText = `[Grok API] I understand you're asking: "${prompt}". This is a simulated response. In production, this would connect to the actual Grok API.`;
          break;
          
        case 'deepseek':
          // Simulate DeepSeek API call (replace with actual API in production)
          responseText = `[DeepSeek API] Processing your query: "${prompt}". This is a simulated response. In production, this would connect to the actual DeepSeek API.`;
          break;
          
        default:
          throw new Error(`Unknown model: ${modelName}`);
      }
      
      sendResponse({ success: true, response: responseText });
    } catch (error) {
      console.error('LLM request error:', error);
      sendResponse({ 
        success: false, 
        error: error.message || 'Unknown error' 
      });
    }
    
    return true; // Indicates that sendResponse will be called asynchronously
  }
});

// Function to call Gemini API
async function callGeminiAPI(prompt) {
  const url = `https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${GEMINI_API_KEY}`;
  
  const requestBody = {
    contents: [{
      parts: [{
        text: prompt
      }]
    }]
  };

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    if (data.candidates && data.candidates[0] && data.candidates[0].content) {
      return data.candidates[0].content.parts[0].text;
    } else {
      throw new Error('Invalid response format from Gemini API');
    }
  } catch (error) {
    console.error('Gemini API call failed:', error);
    throw new Error(`Gemini API error: ${error.message}`);
  }
}

// Handle extension installation
chrome.runtime.onInstalled.addListener(() => {
  console.log('Moko Browser AI Assistant installed');
});
