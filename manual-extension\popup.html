<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON><PERSON>er AI Assistant</title>
    <style>
      body {
        width: 300px;
        padding: 20px;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background-color: #1e1e1e;
        color: #d4d4d4;
        margin: 0;
      }
      .header {
        text-align: center;
        margin-bottom: 20px;
      }
      .icon {
        font-size: 32px;
        margin-bottom: 10px;
      }
      .title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 5px;
      }
      .subtitle {
        font-size: 12px;
        color: #888;
      }
      .button {
        width: 100%;
        padding: 12px;
        margin: 8px 0;
        background-color: #0e639c;
        color: white;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
      }
      .button:hover {
        background-color: #1177bb;
      }
      .button-secondary {
        background-color: #2d2d30;
        border: 1px solid #464647;
      }
      .button-secondary:hover {
        background-color: #3c3c3c;
      }
      .shortcut {
        text-align: center;
        font-size: 11px;
        color: #888;
        margin-top: 15px;
      }
    </style>
  </head>
  <body>
    <div class="header">
      <div class="icon">🤖</div>
      <div class="title">Moko AI Assistant</div>
      <div class="subtitle">AI-powered browser extension</div>
    </div>

    <button id="openSidepanel" class="button">
      Open AI Sidepanel
    </button>

    <button id="getCurrentUrl" class="button button-secondary">
      Get Current URL
    </button>

    <div class="shortcut">
      Press <strong>Ctrl+B</strong> to toggle sidepanel
    </div>

    <script src="popup.js"></script>
  </body>
</html>
