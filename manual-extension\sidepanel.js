// Moko AI Assistant Sidepanel
class MokoSidepanel {
  constructor() {
    this.chatHistory = [];
    this.isLoading = false;
    this.isRecording = false;
    this.currentModel = 'gemini-pro';
    
    this.initializeElements();
    this.attachEventListeners();
  }

  initializeElements() {
    this.chatArea = document.getElementById('chatArea');
    this.emptyState = document.getElementById('emptyState');
    this.chatInput = document.getElementById('chatInput');
    this.sendButton = document.getElementById('sendButton');
    this.clearButton = document.getElementById('clearButton');
    this.modelDropdown = document.getElementById('modelDropdown');
    this.voiceButton = document.getElementById('voiceButton');
    this.imageButton = document.getElementById('imageButton');
    this.docsButton = document.getElementById('docsButton');
    this.pasteButton = document.getElementById('pasteButton');
    this.fileInput = document.getElementById('fileInput');
    this.imageInput = document.getElementById('imageInput');
  }

  attachEventListeners() {
    // Chat input events
    this.chatInput.addEventListener('input', () => this.updateSendButton());
    this.chatInput.addEventListener('keydown', (e) => this.handleKeyDown(e));
    
    // Button events
    this.sendButton.addEventListener('click', () => this.handleSend());
    this.clearButton.addEventListener('click', () => this.clearChat());
    this.modelDropdown.addEventListener('change', (e) => this.currentModel = e.target.value);
    
    // Action button events
    this.voiceButton.addEventListener('click', () => this.handleVoice());
    this.imageButton.addEventListener('click', () => this.handleImage());
    this.docsButton.addEventListener('click', () => this.handleDocs());
    this.pasteButton.addEventListener('click', () => this.handlePaste());
    
    // File input events
    this.fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
    this.imageInput.addEventListener('change', (e) => this.handleImageSelect(e));
  }

  updateSendButton() {
    const hasText = this.chatInput.value.trim().length > 0;
    this.sendButton.disabled = !hasText || this.isLoading;
    this.sendButton.textContent = this.isLoading ? '...' : 'Send';
  }

  handleKeyDown(e) {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      this.handleSend();
    }
  }

  async handleSend() {
    const message = this.chatInput.value.trim();
    if (!message || this.isLoading) return;

    // Add user message
    this.addMessage('user', message);
    this.chatInput.value = '';
    this.updateSendButton();

    // Set loading state
    this.isLoading = true;
    this.updateSendButton();

    // Add thinking message
    const thinkingId = this.addMessage('ai', 'Thinking...');

    try {
      // Send message to background script
      const response = await chrome.runtime.sendMessage({
        type: 'llmRequest',
        modelName: this.currentModel,
        prompt: message
      });

      // Update the thinking message with the response
      this.updateMessage(thinkingId, response.success ? response.response : `Error: ${response.error}`);
    } catch (error) {
      this.updateMessage(thinkingId, `Communication Error: ${error.message}`);
    } finally {
      this.isLoading = false;
      this.updateSendButton();
    }
  }

  addMessage(type, content) {
    const messageId = Date.now();
    const message = {
      id: messageId,
      type,
      content,
      timestamp: new Date()
    };

    this.chatHistory.push(message);
    this.renderMessage(message);
    this.hideEmptyState();
    this.scrollToBottom();

    return messageId;
  }

  updateMessage(messageId, newContent) {
    const messageIndex = this.chatHistory.findIndex(msg => msg.id === messageId);
    if (messageIndex !== -1) {
      this.chatHistory[messageIndex].content = newContent;
      this.renderMessages();
    }
  }

  renderMessage(message) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message message-${message.type}`;
    messageDiv.innerHTML = `
      <div class="message-bubble message-bubble-${message.type}">
        ${message.content}
      </div>
      <div class="message-time message-time-${message.type}">
        ${message.timestamp.toLocaleTimeString()}
      </div>
    `;

    this.chatArea.appendChild(messageDiv);
  }

  renderMessages() {
    // Clear existing messages (except empty state)
    const messages = this.chatArea.querySelectorAll('.message');
    messages.forEach(msg => msg.remove());

    // Render all messages
    this.chatHistory.forEach(message => this.renderMessage(message));
    this.scrollToBottom();
  }

  hideEmptyState() {
    this.emptyState.style.display = 'none';
  }

  showEmptyState() {
    this.emptyState.style.display = 'block';
  }

  scrollToBottom() {
    this.chatArea.scrollTop = this.chatArea.scrollHeight;
  }

  clearChat() {
    this.chatHistory = [];
    const messages = this.chatArea.querySelectorAll('.message');
    messages.forEach(msg => msg.remove());
    this.showEmptyState();
  }

  handleVoice() {
    this.isRecording = !this.isRecording;
    this.voiceButton.textContent = this.isRecording ? '🎤 Stop' : '🎤 Voice';
    this.voiceButton.className = this.isRecording ? 
      'action-button action-button-recording' : 'action-button';
    
    // Voice recording logic would go here
    console.log('Voice recording:', this.isRecording ? 'started' : 'stopped');
  }

  handleImage() {
    this.imageInput.click();
  }

  handleDocs() {
    this.fileInput.click();
  }

  async handlePaste() {
    try {
      const text = await navigator.clipboard.readText();
      this.chatInput.value += text;
      this.updateSendButton();
    } catch (error) {
      console.error('Failed to read clipboard:', error);
    }
  }

  handleFileSelect(e) {
    const file = e.target.files[0];
    if (file) {
      console.log('File selected:', file.name);
      // File processing logic would go here
    }
  }

  handleImageSelect(e) {
    const file = e.target.files[0];
    if (file) {
      console.log('Image selected:', file.name);
      // Image processing logic would go here
    }
  }
}

// Initialize the sidepanel when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new MokoSidepanel();
});
