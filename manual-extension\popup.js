// Popup script for <PERSON><PERSON> AI Assistant

document.addEventListener('DOMContentLoaded', () => {
  const openSidepanelButton = document.getElementById('openSidepanel');
  const getCurrentUrlButton = document.getElementById('getCurrentUrl');

  // Open sidepanel
  openSidepanelButton.addEventListener('click', async () => {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tab && tab.id) {
        await chrome.sidePanel.open({ tabId: tab.id });
        window.close();
      }
    } catch (error) {
      console.error('Failed to open sidepanel:', error);
    }
  });

  // Get current URL
  getCurrentUrlButton.addEventListener('click', async () => {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tab && tab.url) {
        await navigator.clipboard.writeText(tab.url);
        getCurrentUrlButton.textContent = 'URL Copied!';
        setTimeout(() => {
          getCurrentUrlButton.textContent = 'Get Current URL';
        }, 2000);
      }
    } catch (error) {
      console.error('Failed to get current URL:', error);
    }
  });
});
