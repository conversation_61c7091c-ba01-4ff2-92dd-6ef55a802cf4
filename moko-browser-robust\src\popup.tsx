import React, { useState } from 'react';
import ReactDOM from 'react-dom/client';

const Popup = () => {
  const [query, setQuery] = useState('');
  const [currentUrl, setCurrentUrl] = useState('');

  const handleNavigateOrSearch = () => {
    if (query.startsWith('http://') || query.startsWith('https://')) {
      chrome.tabs.update({ url: query });
    } else {
      chrome.tabs.create({ url: `https://www.google.com/search?q=${encodeURIComponent(query)}` });
    }
  };

  const handleGetCurrentUrl = () => {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs[0] && tabs[0].url) {
        setCurrentUrl(tabs[0].url);
      }
    });
  };

  return (
    <div style={{ width: '300px', padding: '10px' }}>
      <h2><PERSON><PERSON>er</h2>
      <input
        type="text"
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        placeholder="Enter URL or search query"
        style={{ width: '100%', padding: '8px', marginBottom: '10px', boxSizing: 'border-box' }}
      />
      <button
        onClick={handleNavigateOrSearch}
        style={{ width: '100%', padding: '10px', backgroundColor: '#4CAF50', color: 'white', border: 'none', cursor: 'pointer', marginBottom: '10px' }}
      >
        Go / Search
      </button>

      <hr style={{ borderTop: '1px solid #eee', margin: '15px 0' }} />

      <h3>Current Tab Info</h3>
      <button
        onClick={handleGetCurrentUrl}
        style={{ width: '100%', padding: '10px', backgroundColor: '#007bff', color: 'white', border: 'none', cursor: 'pointer', marginBottom: '10px' }}
      >
        Get Current URL
      </button>
      {currentUrl && (
        <p style={{ wordBreak: 'break-all', fontSize: '0.9em', color: '#555' }}>
          URL: <a href={currentUrl} target="_blank" rel="noopener noreferrer">{currentUrl}</a>
        </p>
      )}
    </div>
  );
};

export default Popup;