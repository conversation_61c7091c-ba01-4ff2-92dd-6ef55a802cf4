// Content script for <PERSON><PERSON> Browser AI Assistant

console.log('<PERSON><PERSON>rows<PERSON> AI Assistant content script loaded');

// Listen for messages from the background script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.type === 'getPageContent') {
    // Extract page content
    const pageContent = document.body.innerText;
    sendResponse({ content: pageContent });
    return true;
  }
  
  if (request.type === 'getSelectedText') {
    // Get selected text
    const selectedText = window.getSelection().toString();
    sendResponse({ selectedText: selectedText });
    return true;
  }
});

// Optional: Add a visual indicator that the extension is active
const indicator = document.createElement('div');
indicator.style.cssText = `
  position: fixed;
  top: 10px;
  right: 10px;
  background: #0e639c;
  color: white;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
  font-family: Arial, sans-serif;
  z-index: 10000;
  opacity: 0.8;
  pointer-events: none;
`;
indicator.textContent = '🤖 Moko AI';

// Show indicator briefly when page loads
document.addEventListener('DOMContentLoaded', () => {
  document.body.appendChild(indicator);
  setTimeout(() => {
    if (indicator.parentNode) {
      indicator.parentNode.removeChild(indicator);
    }
  }, 3000);
});
