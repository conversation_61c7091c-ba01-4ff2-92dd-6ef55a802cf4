Open Source & Recommended Repos for Building Browser Extensions
If you're a small developer wanting to create a browser extension—especially one powered by AI—there are several modern, open source frameworks and codebases that can help you build, scale, and maintain quality browser extensions for your product.

Top Open Source Frameworks & Boilerplates
1. Plasmo Framework
What it is: Full-featured framework for building browser extensions (like Next.js for extensions).

Features:

1st-class React & TypeScript support

Hot Module Replacement (HMR)

Content scripts, background, UI, storage & messaging APIs

Multi-browser support (Chrome, Firefox, Edge, etc.)

Automated deployment and manifest management

Optional Vue and Svelte support

Best for: Fast prototyping, production apps, and React-focused developers.

Repo: PlasmoHQ/plasmo

2. WXT
What it is: Modern cross-browser extension framework with TypeScript by default.

Features:

Write once, deploy on Chrome, Firefox, Edge, Safari, and other Chromium browsers

Fast HMR for UI and content scripts

Automated manifest generation

Easy project structure and integrated publishing tools

Frontend framework agnostic

Best for: Teams and solo devs who want a Next.js/Nuxt.js-like workflow for extensions with TypeScript.

Docs: wxt.dev

3. web-extension-starter
What it is: Boilerplate for building cross-browser web extensions.

Features:

Supports React by default, plus TypeScript and ES6 modules

Built-in SASS styling, smart reload

Easy cross-browser builds and manifest tweaks

Best for: Quick start for multi-browser support with React UI.

Learn more: Mentioned in [this comprehensive list of starters].

Sample Open Source AI Extensions
If your product needs AI or automation features, studying or forking these open projects can help you jumpstart development:

Name	Description & Use Case	Repo	Notable Features
Nanobrowser	AI-powered, open-source web automation agent	nanobrowser/nanobrowser	Multi-agent LLM workflows, BYO API keys, privacy-first, supports OpenAI/Anthropic/Ollama/etc.
Ollamazing	Local LLM assistant browser extension connecting to Ollama	Ollamazing repo (find link via [this guide])	Fully open, works offline w/ local models, chat in sidebar, dev tools
**Extension	OS**	Simple right-click AI prompt extension	extensionOS
Why Use These Frameworks?
Speed & Maintainability: Hot reloading, TS support, automated manifests.

Cross-browser: No “Chrome-only”; reach Firefox, Edge, Safari with minimal changes.

Strong Example Base: Lots of open AI extensions use these tools—try forking, then customizing.

Active communities: Helpful for both troubleshooting and feature additions.

Next Steps for a Small Team
Pick WXT or Plasmo (both are robust, but WXT may appeal if you want clean TypeScript and multi-framework support, while Plasmo is React-focused).

Study example open-source AI extensions listed above; fork or adapt code as needed.

Leverage open documentation: Both frameworks have extensive docs, examples, and community tutorials.

Prototype, test, and iterate quickly with hot reloading and easy publishing tools.

Contribute or seek help from the communities around these tools—they are responsive and active.

References:
For in-depth docs and code samples, see [PlasmoHQ/plasmo], [WXT docs], [Nanobrowser], and curated open extension starter lists. You’ll find modern developer experience that makes shipping a new extension approachable even for a small company or indie dev.