<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON><PERSON> AI Assistant</title>
    <style>
      body {
        margin: 0;
        font-family: '<PERSON>sol<PERSON>', 'Monaco', 'Courier New', monospace;
        background-color: #1e1e1e;
        color: #d4d4d4;
        height: 100vh;
        overflow: hidden;
      }
      #root {
        height: 100vh;
        display: flex;
        flex-direction: column;
      }

      /* Header Styles */
      .header {
        padding: 12px 16px;
        border-bottom: 1px solid #333;
        background-color: #252526;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      .header-title {
        display: flex;
        align-items: center;
        gap: 8px;
      }
      .header-icon {
        font-size: 16px;
        font-weight: bold;
        color: #569cd6;
      }
      .header-text {
        font-size: 14px;
        font-weight: 500;
      }
      .clear-button {
        background: none;
        border: none;
        color: #cccccc;
        cursor: pointer;
        font-size: 12px;
        padding: 4px 8px;
        border-radius: 3px;
      }
      .clear-button:hover {
        background-color: #333;
      }

      /* Model Selection */
      .model-selection {
        padding: 12px 16px;
        border-bottom: 1px solid #333;
        background-color: #2d2d30;
      }
      .model-dropdown {
        width: 100%;
        padding: 6px 8px;
        background-color: #3c3c3c;
        color: #cccccc;
        border: 1px solid #464647;
        border-radius: 3px;
        font-size: 12px;
      }

      /* Chat Area */
      .chat-area {
        flex: 1;
        overflow-y: auto;
        padding: 16px;
        background-color: #1e1e1e;
      }
      .empty-state {
        text-align: center;
        color: #6a6a6a;
        font-size: 13px;
        margin-top: 40px;
      }
      .empty-icon {
        font-size: 24px;
        margin-bottom: 8px;
      }
      .empty-subtitle {
        font-size: 11px;
        margin-top: 4px;
      }
      .message {
        margin-bottom: 16px;
        display: flex;
        flex-direction: column;
      }
      .message-user {
        align-items: flex-end;
      }
      .message-ai {
        align-items: flex-start;
      }
      .message-bubble {
        max-width: 85%;
        padding: 8px 12px;
        border-radius: 8px;
        font-size: 13px;
        line-height: 1.4;
        word-break: break-word;
      }
      .message-bubble-user {
        background-color: #0e639c;
        color: #ffffff;
      }
      .message-bubble-ai {
        background-color: #2d2d30;
        color: #d4d4d4;
      }
      .message-time {
        font-size: 10px;
        color: #6a6a6a;
        margin-top: 4px;
      }
      .message-time-user {
        margin-right: 12px;
      }
      .message-time-ai {
        margin-left: 12px;
      }

      /* Input Area */
      .input-area {
        border-top: 1px solid #333;
        background-color: #252526;
        padding: 12px 16px;
      }
      .action-buttons {
        display: flex;
        gap: 8px;
        margin-bottom: 8px;
        justify-content: space-between;
      }
      .action-button {
        flex: 1;
        padding: 6px 8px;
        background-color: #2d2d30;
        color: #cccccc;
        border: 1px solid #464647;
        border-radius: 3px;
        cursor: pointer;
        font-size: 11px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;
      }
      .action-button:hover {
        background-color: #3c3c3c;
      }
      .action-button-recording {
        background-color: #d73a49;
        color: #ffffff;
      }
      .chat-input-container {
        display: flex;
        gap: 8px;
        align-items: flex-end;
      }
      .chat-textarea {
        flex: 1;
        min-height: 36px;
        max-height: 120px;
        padding: 8px 12px;
        background-color: #3c3c3c;
        color: #d4d4d4;
        border: 1px solid #464647;
        border-radius: 4px;
        font-size: 13px;
        font-family: inherit;
        resize: none;
        outline: none;
      }
      .chat-textarea:focus {
        border-color: #007acc;
      }
      .send-button {
        padding: 8px 16px;
        background-color: #0e639c;
        color: #ffffff;
        border: 1px solid #464647;
        border-radius: 4px;
        cursor: pointer;
        font-size: 13px;
        font-weight: 500;
        min-width: 60px;
      }
      .send-button:disabled {
        background-color: #2d2d30;
        color: #6a6a6a;
        cursor: not-allowed;
      }
      .hidden {
        display: none;
      }
    </style>
  </head>
  <body>
    <div id="root">
      <!-- Header -->
      <div class="header">
        <div class="header-title">
          <span class="header-icon">🤖</span>
          <span class="header-text">Moko AI Assistant</span>
        </div>
        <button id="clearButton" class="clear-button">Clear</button>
      </div>

      <!-- Model Selection -->
      <div class="model-selection">
        <select id="modelDropdown" class="model-dropdown">
          <option value="gemini-pro">🔮 Gemini Pro</option>
          <option value="grok">⚡ Grok</option>
          <option value="deepseek">🧠 DeepSeek</option>
        </select>
      </div>

      <!-- Chat Area -->
      <div id="chatArea" class="chat-area">
        <div id="emptyState" class="empty-state">
          <div class="empty-icon">💬</div>
          <div>Start a conversation with your AI assistant</div>
          <div class="empty-subtitle">
            Use voice, upload files, or type your message below
          </div>
        </div>
        <!-- Messages will be added here dynamically -->
      </div>

      <!-- Input Area -->
      <div class="input-area">
        <!-- Action Buttons -->
        <div class="action-buttons">
          <button id="voiceButton" class="action-button">
            🎤 Voice
          </button>
          <button id="imageButton" class="action-button">
            🖼️ Image
          </button>
          <button id="docsButton" class="action-button">
            📄 Docs
          </button>
          <button id="pasteButton" class="action-button">
            📋 Paste
          </button>
        </div>

        <!-- Chat Input -->
        <div class="chat-input-container">
          <textarea
            id="chatInput"
            class="chat-textarea"
            placeholder="Type your message... (Enter to send, Shift+Enter for new line)"
          ></textarea>
          <button id="sendButton" class="send-button" disabled>
            Send
          </button>
        </div>

        <!-- Hidden file inputs -->
        <input
          id="fileInput"
          type="file"
          accept=".txt,.pdf,.doc,.docx,.md"
          class="hidden"
        />
        <input
          id="imageInput"
          type="file"
          accept="image/*"
          class="hidden"
        />
      </div>
    </div>

    <script src="sidepanel.js"></script>
  </body>
</html>
