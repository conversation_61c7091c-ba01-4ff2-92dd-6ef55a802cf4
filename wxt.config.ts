import { defineConfig } from 'wxt'
import react from '@vitejs/plugin-react'

// See https://wxt.dev/api/config.html
export default defineConfig({
  srcDir: '.',
  vite: () => ({
    plugins: [react()],
    define: {
      'import.meta.env.GEMINI_API_KEY': JSON.stringify(process.env.GEMINI_API_KEY || ''),
      'import.meta.env.GROK_API_KEY': JSON.stringify(process.env.GROK_API_KEY || ''),
      'import.meta.env.DEEPSEEK_API_KEY': JSON.stringify(process.env.DEEPSEEK_API_KEY || ''),
    },
  }),
  manifest: {
    name: 'Moko Browser AI Assistant',
    description: 'AI-powered browser extension with IDE-style sidepanel',
    version: '1.0.0',
    permissions: [
      'activeTab',
      'sidePanel',
      'storage'
    ],
    host_permissions: [
      '<all_urls>'
    ],
    side_panel: {
      default_path: 'sidepanel.html'
    },
    commands: {
      '_execute_side_panel_action': {
        suggested_key: {
          default: 'Ctrl+B',
          mac: 'Command+B'
        },
        description: 'Toggle Moko AI Side Panel'
      }
    }
  }
})