/// <reference types="chrome" />
import React, { useState, useEffect } from 'react';
import ReactDOM from 'react-dom/client';

export const BottomChat = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [chatInput, setChatInput] = useState('');
  const [chatHistory, setChatHistory] = useState<string[]>([]);
  const [llmModel, setLlmModel] = useState('gemini-pro'); // Default model

  useEffect(() => {
    // Listener for messages from the background script
    chrome.runtime.onMessage.addListener((request: any, sender: chrome.runtime.MessageSender, sendResponse: (response?: any) => void) => {
      if (request.type === 'getPageContent') {
        const pageContent = document.body.innerText; // Basic content extraction
        sendResponse({ content: pageContent });
        return true; // Indicates that sendResponse will be called asynchronously
      }
    });
  }, []);

  const handleChat = async () => {
    if (chatInput.trim() === '') return;

    const userMessage = `You: ${chatInput}`;
    setChatHistory((prev) => [...prev, userMessage]);
    setChatInput('');

    setChatHistory((prev) => [...prev, `AI (${llmModel}): Thinking...`]);

    try {
      const currentUrl = window.location.href;
      const selectedText = window.getSelection()?.toString() || '';

      const response = await chrome.runtime.sendMessage({
        type: 'llmRequest',
        modelName: llmModel,
        prompt: { type: 'text', text: chatInput, url: currentUrl, selectedText: selectedText }, // Send as structured prompt with context
      });

      if (response.success) {
        setChatHistory((prev) => {
          const newHistory = [...prev];
          newHistory[newHistory.length - 1] = `AI (${llmModel}): ${response.response}`;
          return newHistory;
        });
      } else {
        setChatHistory((prev) => {
          const newHistory = [...prev];
          newHistory[newHistory.length - 1] = `AI (${llmModel}): Error - ${response.error}`;
          return newHistory;
        });
      }
    } catch (error: unknown) {
      setChatHistory((prev) => {
        const newHistory = [...prev];
        newHistory[newHistory.length - 1] = `AI (${llmModel}): Communication Error - ${error instanceof Error ? error.message : 'An unknown error occurred.'}`;
        return newHistory;
      });
    }
  };

  return (
    <div style={{
      position: 'fixed',
      bottom: '20px',
      right: '20px',
      width: isOpen ? '350px' : '50px',
      height: isOpen ? '450px' : '50px',
      backgroundColor: '#282c34',
      border: '1px solid #61afef',
      borderRadius: isOpen ? '8px' : '25px',
      boxShadow: '0 4px 8px rgba(0, 0, 0, 0.2)',
      zIndex: '9999',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden',
      transition: 'all 0.3s ease-in-out',
      fontFamily: 'Roboto Mono, monospace',
      color: '#abb2bf',
    }}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        style={{
          width: '50px',
          height: '50px',
          borderRadius: '25px',
          backgroundColor: '#56b6c2',
          color: 'white',
          border: 'none',
          cursor: 'pointer',
          fontSize: '24px',
          position: 'absolute',
          bottom: isOpen ? 'auto' : '0',
          right: isOpen ? 'auto' : '0',
          top: isOpen ? '10px' : 'auto',
          left: isOpen ? '10px' : 'auto',
          zIndex: '10000',
        }}
      >
        {isOpen ? 'X' : '💬'}
      </button>

      {isOpen && (
        <div style={{ flexGrow: 1, display: 'flex', flexDirection: 'column', padding: '10px', paddingTop: '60px' }}>
          <h3 style={{ color: '#61dafb', marginTop: '0', marginBottom: '10px' }}>Bottom Chat</h3>

          {/* LLM Model Dropdown */}
          <div style={{ marginBottom: '10px' }}>
            <label htmlFor="llm-model-bottom" style={{ display: 'block', marginBottom: '5px', fontSize: '0.8em', color: '#c678dd' }}>Model:</label>
            <select
              id="llm-model-bottom"
              value={llmModel}
              onChange={(e) => setLlmModel(e.target.value)}
              style={{
                width: '100%',
                padding: '5px',
                backgroundColor: '#3b4048',
                color: '#abb2bf',
                border: '1px solid #61afef',
                borderRadius: '4px',
                fontSize: '0.9em',
              }}
            >
              <option value="gemini-pro">Gemini Pro</option>
              <option value="grok">Grok (Placeholder)</option>
              <option value="deepseek">DeepSeek (Placeholder)</option>
            </select>
          </div>

          {/* Chat History */}
          <div style={{
            flexGrow: 1,
            backgroundColor: '#21252b',
            border: '1px solid #3e4451',
            borderRadius: '4px',
            padding: '8px',
            overflowY: 'auto',
            marginBottom: '10px',
            whiteSpace: 'pre-wrap',
            wordBreak: 'break-word',
            fontSize: '0.9em',
          }}>
            {chatHistory.map((msg, index) => (
              <p key={index} style={{ margin: '3px 0', color: msg.startsWith('You:') ? '#98c379' : '#e06c75' }}>{msg}</p>
            ))}
          </div>

          {/* Chat Input */}
          <textarea
            value={chatInput}
            onChange={(e) => setChatInput(e.target.value)}
            placeholder="Type here..."
            rows={3}
            style={{
              width: '100%',
              padding: '8px',
              backgroundColor: '#3b4048',
              color: '#abb2bf',
              border: '1px solid #61afef',
              borderRadius: '4px',
              marginBottom: '5px',
              resize: 'none',
              boxSizing: 'border-box',
              fontSize: '0.9em',
            }}
          />

          <button
            onClick={handleChat}
            style={{
              width: '100%',
              padding: '8px',
              backgroundColor: '#56b6c2',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '0.9em',
            }}
          >
            Send
          </button>
        </div>
      )}
    </div>
  );
};

const rootContainer = document.createElement('div');
rootContainer.id = 'moko-chat-root'; // Assign an ID for easier identification

document.addEventListener('DOMContentLoaded', () => {
  document.body.appendChild(rootContainer);
  const root = ReactDOM.createRoot(rootContainer);
  root.render(<React.StrictMode><BottomChat /></React.StrictMode>);
});

