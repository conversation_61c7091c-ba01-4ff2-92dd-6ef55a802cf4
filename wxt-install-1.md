Run the init command, and follow the instructions.


PNPM

Bun

NPM

Yarn

pnpm dlx wxt@latest init
Starter Templates:

TypeScript LogoVanilla
Vue LogoVue
React LogoReact
Svelte LogoSvelte
Solid LogoSolid

All templates use TypeScript by default. To use JavaScript, change the file extensions.

Demo
wxt init demo

Once you've run the dev command, continue to Next Steps!

From Scratch
Create a new project


PNPM

Bun

NPM

Yarn

cd my-project
pnpm init
Install WXT:


PNPM

Bun

NPM

Yarn

pnpm i -D wxt
Add an entrypoint, my-project/entrypoints/background.ts:


ts

export default defineBackground(() => {
  console.log('Hello world!');
});
Add scripts to your package.json:

package.json

{
  "scripts": {
    "dev": "wxt", 
    "dev:firefox": "wxt -b firefox", 
    "build": "wxt build", 
    "build:firefox": "wxt build -b firefox", 
    "zip": "wxt zip", 
    "zip:firefox": "wxt zip -b firefox", 
    "postinstall": "wxt prepare"
  }
}
Run your extension in dev mode


PNPM

Bun

NPM

Yarn

pnpm dev
WXT will automatically open a browser window with your extension installed.

Next Steps
Keep reading on about WXT's Project Structure and other essential concepts to learn
Configure automatic browser startup during dev mode
Explore WXT's example library to see how to use specific APIs or perform common tasks
Checkout the community page for a list of resources made by the community!
Edit this page
Last updated: 04/06/2025, 15:40