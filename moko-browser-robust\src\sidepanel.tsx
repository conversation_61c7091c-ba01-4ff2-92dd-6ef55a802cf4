/// <reference types="chrome" />
import React, { useState } from 'react';
import ReactDOM from 'react-dom/client';

const SidePanel = () => {
  const [llmModel, setLlmModel] = useState('gemini-pro');
  const [chatInput, setChatInput] = useState('');
  const [chatHistory, setChatHistory] = useState<string[]>([]);

  const handleChat = async () => {
    if (chatInput.trim() === '') return;

    const userMessage = `You: ${chatInput}`;
    setChatHistory((prev) => [...prev, userMessage]);
    setChatInput('');

    setChatHistory((prev) => [...prev, `AI (${llmModel}): Thinking...`]);

    try {
      const response = await chrome.runtime.sendMessage({
        type: 'llmRequest',
        modelName: llmModel,
        prompt: chatInput,
      });

      if (response.success) {
        setChatHistory((prev) => {
          const newHistory = [...prev];
          newHistory[newHistory.length - 1] = `AI (${llmModel}): ${response.response}`;
          return newHistory;
        });
      } else {
        setChatHistory((prev) => {
          const newHistory = [...prev];
          newHistory[newHistory.length - 1] = `AI (${llmModel}): Error - ${response.error}`;
          return newHistory;
        });
      }
    } catch (error) {
      setChatHistory((prev) => {
        const newHistory = [...prev];
        newHistory[newHistory.length - 1] = `AI (${llmModel}): Communication Error - ${error.message}`;
        return newHistory;
      });
    }
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', height: '100%', padding: '10px', boxSizing: 'border-box' }}>
      <h2 style={{ color: '#61dafb', marginBottom: '15px' }}>Moko AI Panel</h2>

      {/* LLM Model Dropdown */}
      <div style={{ marginBottom: '15px' }}>
        <label htmlFor="llm-model" style={{ display: 'block', marginBottom: '5px', color: '#c678dd' }}>Select LLM Model:</label>
        <select
          id="llm-model"
          value={llmModel}
          onChange={(e) => setLlmModel(e.target.value)}
          style={{
            width: '100%',
            padding: '8px',
            backgroundColor: '#3b4048',
            color: '#abb2bf',
            border: '1px solid #61afef',
            borderRadius: '4px',
          }}
        >
          <option value="gemini-pro">Gemini Pro (API)</option>
          <option value="grok">Grok (API)</option>
          <option value="deepseek">DeepSeek (API)</option>
        </select>
      </div>

      {/* Chat History */}
      <div style={{
        flexGrow: 1,
        backgroundColor: '#21252b',
        border: '1px solid #3e4451',
        borderRadius: '4px',
        padding: '10px',
        overflowY: 'auto',
        marginBottom: '10px',
        whiteSpace: 'pre-wrap',
        wordBreak: 'break-word',
      }}>
        {chatHistory.map((msg, index) => (
          <p key={index} style={{ margin: '5px 0', color: msg.startsWith('You:') ? '#98c379' : '#e06c75' }}>{msg}</p>
        ))}
      </div>

      {/* Chat Input */}
      <textarea
        value={chatInput}
        onChange={(e) => setChatInput(e.target.value)}
        placeholder="Type your query or task here..."
        rows={4}
        style={{
          width: '100%',
          padding: '10px',
          backgroundColor: '#3b4048',
          color: '#abb2bf',
          border: '1px solid #61afef',
          borderRadius: '4px',
          marginBottom: '10px',
          resize: 'vertical',
          boxSizing: 'border-box',
        }}
      />

      {/* Action Buttons */}
      <div style={{ display: 'flex', flexWrap: 'wrap', gap: '5px' }}>
        <button
          onClick={handleChat}
          style={{
            flex: '1 1 auto',
            padding: '10px',
            backgroundColor: '#56b6c2',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            minWidth: 'calc(50% - 5px)',
          }}
        >
          Chat
        </button>
        <button
          style={{
            flex: '1 1 auto',
            padding: '10px',
            backgroundColor: '#c678dd',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'not-allowed',
            minWidth: 'calc(50% - 5px)',
          }}
          disabled
        >
          Image (Soon)
        </button>
        <button
          style={{
            flex: '1 1 auto',
            padding: '10px',
            backgroundColor: '#e06c75',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'not-allowed',
            minWidth: 'calc(50% - 5px)',
          }}
          disabled
        >
          File/Docs (Soon)
        </button>
        <button
          style={{
            flex: '1 1 auto',
            padding: '10px',
            backgroundColor: '#98c379',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'not-allowed',
            minWidth: 'calc(50% - 5px)',
          }}
          disabled
        >
          Voice (Soon)
        </button>
        <button
          style={{
            flex: '1 1 auto',
            padding: '10px',
            backgroundColor: '#61afef',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'not-allowed',
            minWidth: 'calc(50% - 5px)',
          }}
          disabled
        >
          Copy/Paste (Soon)
        </button>
      </div>
    </div>
  );
};

const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement);
root.render(
  <React.StrictMode>
    <SidePanel />
  </React.StrictMode>
);