/// <reference types="chrome" />
import React, { useState, useRef } from 'react';
import ReactDOM from 'react-dom/client';

const SidePanel = () => {
  const [llmModel, setLlmModel] = useState('gemini-pro');
  const [chatInput, setChatInput] = useState('');
  const [chatHistory, setChatHistory] = useState<Array<{type: 'user' | 'ai', content: string, timestamp: Date}>>([]);
  const [isRecording, setIsRecording] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const imageInputRef = useRef<HTMLInputElement>(null);

  const handleChat = async () => {
    if (chatInput.trim() === '') return;

    const userMessage = {
      type: 'user' as const,
      content: chatInput,
      timestamp: new Date()
    };

    setChatHistory((prev) => [...prev, userMessage]);
    setChatInput('');

    const thinkingMessage = {
      type: 'ai' as const,
      content: 'Thinking...',
      timestamp: new Date()
    };
    setChatHistory((prev) => [...prev, thinkingMessage]);

    try {
      const response = await chrome.runtime.sendMessage({
        type: 'llmRequest',
        modelName: llmModel,
        prompt: chatInput,
      });

      setChatHistory((prev) => {
        const newHistory = [...prev];
        newHistory[newHistory.length - 1] = {
          type: 'ai',
          content: response.success ? response.response : `Error - ${response.error}`,
          timestamp: new Date()
        };
        return newHistory;
      });
    } catch (error) {
      setChatHistory((prev) => {
        const newHistory = [...prev];
        newHistory[newHistory.length - 1] = {
          type: 'ai',
          content: `Communication Error - ${error instanceof Error ? error.message : 'Unknown error'}`,
          timestamp: new Date()
        };
        return newHistory;
      });
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleChat();
    }
  };

  const handleVoiceToggle = () => {
    setIsRecording(!isRecording);
    // Voice recording logic would go here
  };

  const handleImageUpload = () => {
    imageInputRef.current?.click();
  };

  const handleFileUpload = () => {
    fileInputRef.current?.click();
  };

  const handleCopyPaste = async () => {
    try {
      const text = await navigator.clipboard.readText();
      setChatInput(prev => prev + text);
    } catch (err) {
      console.error('Failed to read clipboard:', err);
    }
  };

  const clearChat = () => {
    setChatHistory([]);
  };

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      height: '100vh',
      backgroundColor: '#1e1e1e',
      color: '#d4d4d4',
      fontFamily: "'Consolas', 'Monaco', 'Courier New', monospace"
    }}>
      {/* Header */}
      <div style={{
        padding: '12px 16px',
        borderBottom: '1px solid #333',
        backgroundColor: '#252526',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <span style={{ fontSize: '16px', fontWeight: 'bold', color: '#569cd6' }}>🤖</span>
          <span style={{ fontSize: '14px', fontWeight: '500' }}>Moko AI Assistant</span>
        </div>
        <button
          onClick={clearChat}
          style={{
            background: 'none',
            border: 'none',
            color: '#cccccc',
            cursor: 'pointer',
            fontSize: '12px',
            padding: '4px 8px',
            borderRadius: '3px'
          }}
          onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#333'}
          onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
        >
          Clear
        </button>
      </div>

      {/* Model Selection */}
      <div style={{ padding: '12px 16px', borderBottom: '1px solid #333', backgroundColor: '#2d2d30' }}>
        <select
          value={llmModel}
          onChange={(e) => setLlmModel(e.target.value)}
          style={{
            width: '100%',
            padding: '6px 8px',
            backgroundColor: '#3c3c3c',
            color: '#cccccc',
            border: '1px solid #464647',
            borderRadius: '3px',
            fontSize: '12px'
          }}
        >
          <option value="gemini-pro">🔮 Gemini Pro</option>
          <option value="grok">⚡ Grok</option>
          <option value="deepseek">🧠 DeepSeek</option>
        </select>
      </div>

      {/* Chat Area */}
      <div style={{
        flex: 1,
        overflowY: 'auto',
        padding: '16px',
        backgroundColor: '#1e1e1e'
      }}>
        {chatHistory.length === 0 ? (
          <div style={{
            textAlign: 'center',
            color: '#6a6a6a',
            fontSize: '13px',
            marginTop: '40px'
          }}>
            <div style={{ fontSize: '24px', marginBottom: '8px' }}>💬</div>
            <div>Start a conversation with your AI assistant</div>
            <div style={{ fontSize: '11px', marginTop: '4px' }}>
              Use voice, upload files, or type your message below
            </div>
          </div>
        ) : (
          chatHistory.map((msg, index) => (
            <div key={index} style={{
              marginBottom: '16px',
              display: 'flex',
              flexDirection: 'column',
              alignItems: msg.type === 'user' ? 'flex-end' : 'flex-start'
            }}>
              <div style={{
                maxWidth: '85%',
                padding: '8px 12px',
                borderRadius: '8px',
                backgroundColor: msg.type === 'user' ? '#0e639c' : '#2d2d30',
                color: msg.type === 'user' ? '#ffffff' : '#d4d4d4',
                fontSize: '13px',
                lineHeight: '1.4',
                wordBreak: 'break-word'
              }}>
                {msg.content}
              </div>
              <div style={{
                fontSize: '10px',
                color: '#6a6a6a',
                marginTop: '4px',
                marginLeft: msg.type === 'user' ? '0' : '12px',
                marginRight: msg.type === 'user' ? '12px' : '0'
              }}>
                {msg.timestamp.toLocaleTimeString()}
              </div>
            </div>
          ))
        )}
      </div>

      {/* Bottom Chat Input Area */}
      <div style={{
        borderTop: '1px solid #333',
        backgroundColor: '#252526',
        padding: '12px 16px'
      }}>
        {/* Action Buttons Row */}
        <div style={{
          display: 'flex',
          gap: '8px',
          marginBottom: '8px',
          justifyContent: 'space-between'
        }}>
          <button
            onClick={handleVoiceToggle}
            style={{
              flex: 1,
              padding: '6px 8px',
              backgroundColor: isRecording ? '#d73a49' : '#2d2d30',
              color: isRecording ? '#ffffff' : '#cccccc',
              border: '1px solid #464647',
              borderRadius: '3px',
              cursor: 'pointer',
              fontSize: '11px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '4px'
            }}
            onMouseOver={(e) => {
              if (!isRecording) e.currentTarget.style.backgroundColor = '#3c3c3c';
            }}
            onMouseOut={(e) => {
              if (!isRecording) e.currentTarget.style.backgroundColor = '#2d2d30';
            }}
          >
            🎤 {isRecording ? 'Stop' : 'Voice'}
          </button>

          <button
            onClick={handleImageUpload}
            style={{
              flex: 1,
              padding: '6px 8px',
              backgroundColor: '#2d2d30',
              color: '#cccccc',
              border: '1px solid #464647',
              borderRadius: '3px',
              cursor: 'pointer',
              fontSize: '11px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '4px'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#3c3c3c'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#2d2d30'}
          >
            🖼️ Image
          </button>

          <button
            onClick={handleFileUpload}
            style={{
              flex: 1,
              padding: '6px 8px',
              backgroundColor: '#2d2d30',
              color: '#cccccc',
              border: '1px solid #464647',
              borderRadius: '3px',
              cursor: 'pointer',
              fontSize: '11px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '4px'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#3c3c3c'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#2d2d30'}
          >
            📄 Docs
          </button>

          <button
            onClick={handleCopyPaste}
            style={{
              flex: 1,
              padding: '6px 8px',
              backgroundColor: '#2d2d30',
              color: '#cccccc',
              border: '1px solid #464647',
              borderRadius: '3px',
              cursor: 'pointer',
              fontSize: '11px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '4px'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#3c3c3c'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#2d2d30'}
          >
            📋 Paste
          </button>
        </div>

        {/* Chat Input */}
        <div style={{ display: 'flex', gap: '8px', alignItems: 'flex-end' }}>
          <textarea
            value={chatInput}
            onChange={(e) => setChatInput(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Type your message... (Enter to send, Shift+Enter for new line)"
            style={{
              flex: 1,
              minHeight: '36px',
              maxHeight: '120px',
              padding: '8px 12px',
              backgroundColor: '#3c3c3c',
              color: '#d4d4d4',
              border: '1px solid #464647',
              borderRadius: '4px',
              fontSize: '13px',
              fontFamily: 'inherit',
              resize: 'none',
              outline: 'none'
            }}
            onFocus={(e) => e.currentTarget.style.borderColor = '#007acc'}
            onBlur={(e) => e.currentTarget.style.borderColor = '#464647'}
          />
          <button
            onClick={handleChat}
            disabled={!chatInput.trim()}
            style={{
              padding: '8px 16px',
              backgroundColor: chatInput.trim() ? '#0e639c' : '#2d2d30',
              color: chatInput.trim() ? '#ffffff' : '#6a6a6a',
              border: '1px solid #464647',
              borderRadius: '4px',
              cursor: chatInput.trim() ? 'pointer' : 'not-allowed',
              fontSize: '13px',
              fontWeight: '500',
              minWidth: '60px'
            }}
          >
            Send
          </button>
        </div>

        {/* Hidden file inputs */}
        <input
          ref={fileInputRef}
          type="file"
          accept=".txt,.pdf,.doc,.docx,.md"
          style={{ display: 'none' }}
          onChange={(e) => {
            const file = e.target.files?.[0];
            if (file) {
              // Handle file upload logic here
              console.log('File selected:', file.name);
            }
          }}
        />
        <input
          ref={imageInputRef}
          type="file"
          accept="image/*"
          style={{ display: 'none' }}
          onChange={(e) => {
            const file = e.target.files?.[0];
            if (file) {
              // Handle image upload logic here
              console.log('Image selected:', file.name);
            }
          }}
        />
      </div>
    </div>
  );
};

const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement);
root.render(
  <React.StrictMode>
    <SidePanel />
  </React.StrictMode>
);